// app/api/accounting/income/template/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import { Budget, BudgetCategory } from '@/models/accounting/Budget';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import * as XLSX from 'xlsx';

export const runtime = 'nodejs';

/**
 * GET /api/accounting/income/template
 * Download income import template
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database to get current budgets and categories
    await connectToDatabase();

    // Get active budgets and categories for reference
    const budgets = await Budget.find({ status: { $in: ['active', 'approved'] } })
      .select('name fiscalYear')
      .lean();
    
    const budgetCategories = await BudgetCategory.find({})
      .select('name type')
      .lean();

    // Create workbook
    const workbook = XLSX.utils.book_new();

    // Template data with examples
    const templateData = [
      {
        date: '2025-01-15',
        source: 'government_subvention',
        amount: 1000000,
        reference: 'GS001',
        description: 'Government subvention for Q1 2025',
        fiscalYear: '2025-2026',
        status: 'draft',
        budget: '2025-2026 TCM Budget',
        budgetCategory: 'Income',
        notes: 'Quarterly government funding'
      },
      {
        date: '2025-01-20',
        source: 'registration_fees',
        amount: 250000,
        reference: 'RF001',
        description: 'Teacher registration fees',
        fiscalYear: '2025-2026',
        status: 'received',
        budget: '2025-2026 TCM Budget',
        budgetCategory: 'Income',
        notes: 'Monthly registration fees collection'
      },
      {
        date: '2025-01-25',
        source: 'licensing_fees',
        amount: 150000,
        reference: 'LF001',
        description: 'Professional licensing fees',
        fiscalYear: '2025-2026',
        status: 'approved',
        budget: '2025-2026 TCM Budget',
        budgetCategory: 'Income',
        notes: 'Professional development licensing'
      }
    ];

    // Create main template worksheet
    const templateWorksheet = XLSX.utils.json_to_sheet(templateData);

    // Set column widths
    templateWorksheet['!cols'] = [
      { wch: 12 }, // date
      { wch: 20 }, // source
      { wch: 15 }, // amount
      { wch: 15 }, // reference
      { wch: 30 }, // description
      { wch: 12 }, // fiscalYear
      { wch: 15 }, // status
      { wch: 25 }, // budget
      { wch: 20 }, // budgetCategory
      { wch: 30 }  // notes
    ];

    XLSX.utils.book_append_sheet(workbook, templateWorksheet, 'Income Template');

    // Create reference sheets
    
    // Income Sources reference
    const sourcesData = [
      { value: 'government_subvention', description: 'Government Subvention' },
      { value: 'registration_fees', description: 'Registration Fees' },
      { value: 'licensing_fees', description: 'Licensing Fees' },
      { value: 'donations', description: 'Donations' },
      { value: 'other', description: 'Other Income' }
    ];
    const sourcesWorksheet = XLSX.utils.json_to_sheet(sourcesData);
    XLSX.utils.book_append_sheet(workbook, sourcesWorksheet, 'Income Sources');

    // Status reference
    const statusData = [
      { value: 'draft', description: 'Draft - Not yet submitted' },
      { value: 'pending_approval', description: 'Pending Approval' },
      { value: 'approved', description: 'Approved but not received' },
      { value: 'received', description: 'Received and processed' },
      { value: 'rejected', description: 'Rejected' },
      { value: 'cancelled', description: 'Cancelled' }
    ];
    const statusWorksheet = XLSX.utils.json_to_sheet(statusData);
    XLSX.utils.book_append_sheet(workbook, statusWorksheet, 'Status Options');

    // Budgets reference
    if (budgets.length > 0) {
      const budgetsData = budgets.map(budget => ({
        name: budget.name,
        fiscalYear: budget.fiscalYear
      }));
      const budgetsWorksheet = XLSX.utils.json_to_sheet(budgetsData);
      XLSX.utils.book_append_sheet(workbook, budgetsWorksheet, 'Available Budgets');
    }

    // Budget Categories reference
    if (budgetCategories.length > 0) {
      const categoriesData = budgetCategories.map(category => ({
        name: category.name,
        type: category.type
      }));
      const categoriesWorksheet = XLSX.utils.json_to_sheet(categoriesData);
      XLSX.utils.book_append_sheet(workbook, categoriesWorksheet, 'Budget Categories');
    }

    // Instructions sheet
    const instructionsData = [
      { field: 'date', description: 'Date of income transaction (YYYY-MM-DD format)', required: 'Yes', example: '2025-01-15' },
      { field: 'source', description: 'Income source type', required: 'Yes', example: 'government_subvention' },
      { field: 'amount', description: 'Income amount (numbers only, no commas)', required: 'Yes', example: '1000000' },
      { field: 'reference', description: 'Unique reference number', required: 'Yes', example: 'GS001' },
      { field: 'description', description: 'Description of the income', required: 'No', example: 'Government subvention for Q1' },
      { field: 'fiscalYear', description: 'Fiscal year (YYYY-YYYY format)', required: 'Yes', example: '2025-2026' },
      { field: 'status', description: 'Income status (defaults to draft)', required: 'No', example: 'draft' },
      { field: 'budget', description: 'Budget name (optional)', required: 'No', example: '2025-2026 TCM Budget' },
      { field: 'budgetCategory', description: 'Budget category name (optional)', required: 'No', example: 'Income' },
      { field: 'notes', description: 'Additional notes', required: 'No', example: 'Quarterly funding' }
    ];
    const instructionsWorksheet = XLSX.utils.json_to_sheet(instructionsData);
    instructionsWorksheet['!cols'] = [
      { wch: 15 }, // field
      { wch: 40 }, // description
      { wch: 10 }, // required
      { wch: 25 }  // example
    ];
    XLSX.utils.book_append_sheet(workbook, instructionsWorksheet, 'Instructions');

    // Generate Excel file
    const excelBuffer = XLSX.write(workbook, { 
      type: 'buffer', 
      bookType: 'xlsx',
      compression: true 
    });

    // Set headers for file download
    const headers = new Headers();
    headers.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    headers.set('Content-Disposition', 'attachment; filename="income-import-template.xlsx"');
    headers.set('Content-Length', excelBuffer.length.toString());

    logger.info('Income import template downloaded', {
      userId: user.id,
      userEmail: user.email,
      budgetsCount: budgets.length,
      categoriesCount: budgetCategories.length
    });

    return new NextResponse(excelBuffer, {
      status: 200,
      headers
    });

  } catch (error: unknown) {
    logger.error('Error generating income import template', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
