// app/(dashboard)/dashboard/auditors/settings/page.tsx
import { Metadata } from "next"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { 
  Settings, 
  Save, 
  RotateCcw,
  Shield,
  Clock,
  Bell,
  Database,
  FileText,
  Users,
  AlertTriangle,
  CheckCircle,
  Mail,
  Calendar
} from "lucide-react"

export const metadata: Metadata = {
  title: "Audit Settings - Auditors",
  description: "Configure audit and compliance settings",
}

export default function AuditSettingsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Audit Settings"
        text="Configure audit and compliance settings for government standards"
      >
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>
          <Button size="sm">
            <Save className="h-4 w-4 mr-2" />
            Save Settings
          </Button>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Retention Policy Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-blue-600" />
              Data Retention Policy
            </CardTitle>
            <CardDescription>
              Configure data retention periods for government compliance
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="retention-period">Retention Period (Years)</Label>
                <Select defaultValue="7">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 Years</SelectItem>
                    <SelectItem value="7">7 Years (Government Standard)</SelectItem>
                    <SelectItem value="10">10 Years</SelectItem>
                    <SelectItem value="permanent">Permanent</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Government standard is 7 years for financial records
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="recovery-window">Recovery Window (Days)</Label>
                <Select defaultValue="90">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 Days</SelectItem>
                    <SelectItem value="60">60 Days</SelectItem>
                    <SelectItem value="90">90 Days (Recommended)</SelectItem>
                    <SelectItem value="120">120 Days</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Period during which deleted items can be recovered
                </p>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h4 className="font-medium">Automatic Cleanup Settings</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Auto-expire recovered items</Label>
                    <p className="text-xs text-muted-foreground">
                      Automatically remove items after recovery window expires
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Archive old audit logs</Label>
                    <p className="text-xs text-muted-foreground">
                      Move audit logs older than 2 years to archive storage
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Deletion Requirements */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-red-600" />
              Deletion Requirements
            </CardTitle>
            <CardDescription>
              Configure requirements for audit-compliant deletions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="min-reason-length">Minimum Reason Length</Label>
                <Input 
                  id="min-reason-length" 
                  type="number" 
                  defaultValue="10" 
                  min="5" 
                  max="100"
                />
                <p className="text-xs text-muted-foreground">
                  Minimum characters required for deletion reason
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="max-reason-length">Maximum Reason Length</Label>
                <Input 
                  id="max-reason-length" 
                  type="number" 
                  defaultValue="1000" 
                  min="100" 
                  max="5000"
                />
                <p className="text-xs text-muted-foreground">
                  Maximum characters allowed for deletion reason
                </p>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h4 className="font-medium">Validation Rules</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Require supervisor approval for high-value deletions</Label>
                    <p className="text-xs text-muted-foreground">
                      Deletions above threshold require additional approval
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Block deletion of approved financial records</Label>
                    <p className="text-xs text-muted-foreground">
                      Prevent deletion of approved income/expenditure records
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Require IP address logging</Label>
                    <p className="text-xs text-muted-foreground">
                      Log IP addresses for all deletion operations
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <Label htmlFor="high-value-threshold">High-Value Threshold (MWK)</Label>
              <Input 
                id="high-value-threshold" 
                type="number" 
                defaultValue="1000000" 
                min="0"
              />
              <p className="text-xs text-muted-foreground">
                Amount above which deletions require supervisor approval
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5 text-orange-600" />
              Notification Settings
            </CardTitle>
            <CardDescription>
              Configure alerts and notifications for audit events
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h4 className="font-medium">Email Notifications</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Daily deletion summary</Label>
                    <p className="text-xs text-muted-foreground">
                      Send daily email with deletion statistics
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>High-value deletion alerts</Label>
                    <p className="text-xs text-muted-foreground">
                      Immediate alerts for high-value deletions
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Recovery deadline warnings</Label>
                    <p className="text-xs text-muted-foreground">
                      Alerts when items are approaching recovery deadline
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <Label htmlFor="notification-emails">Notification Recipients</Label>
              <Textarea 
                id="notification-emails"
                placeholder="Enter email addresses separated by commas"
                defaultValue="<EMAIL>, <EMAIL>"
                rows={3}
              />
              <p className="text-xs text-muted-foreground">
                Email addresses to receive audit notifications
              </p>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="warning-days">Recovery Warning Days</Label>
                <Select defaultValue="7">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="3">3 Days</SelectItem>
                    <SelectItem value="7">7 Days</SelectItem>
                    <SelectItem value="14">14 Days</SelectItem>
                    <SelectItem value="30">30 Days</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Send warnings when items expire in X days
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="report-frequency">Report Frequency</Label>
                <Select defaultValue="weekly">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Frequency for automated compliance reports
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Access Control */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-purple-600" />
              Access Control
            </CardTitle>
            <CardDescription>
              Configure role-based access to audit functions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h4 className="font-medium">Role Permissions</h4>
              <div className="space-y-4">
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium text-sm">SUPER_ADMIN</h5>
                    <Badge className="bg-red-100 text-red-700">Full Access</Badge>
                  </div>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    <li>• View all deleted items and audit trail</li>
                    <li>• Recover any deleted items</li>
                    <li>• Generate all compliance reports</li>
                    <li>• Modify audit settings</li>
                  </ul>
                </div>
                
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium text-sm">AUDITOR</h5>
                    <Badge className="bg-blue-100 text-blue-700">Read-Only</Badge>
                  </div>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    <li>• View all deleted items and audit trail</li>
                    <li>• Generate compliance reports</li>
                    <li>• Export audit data</li>
                    <li>• Cannot recover items or modify settings</li>
                  </ul>
                </div>
                
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium text-sm">Other Roles</h5>
                    <Badge className="bg-gray-100 text-gray-700">No Access</Badge>
                  </div>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    <li>• Cannot access audit module</li>
                    <li>• Can only delete items they created</li>
                    <li>• Subject to audit trail logging</li>
                  </ul>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Enable audit module for Finance Managers</Label>
                  <p className="text-xs text-muted-foreground">
                    Allow Finance Managers read-only access to financial audit data
                  </p>
                </div>
                <Switch />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Enable audit module for HR Managers</Label>
                  <p className="text-xs text-muted-foreground">
                    Allow HR Managers read-only access to HR audit data
                  </p>
                </div>
                <Switch />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Compliance Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-green-600" />
              Compliance Status
            </CardTitle>
            <CardDescription>
              Current compliance status with government standards
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">7-year retention policy active</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">90-day recovery window configured</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Mandatory deletion reasons enforced</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Complete audit trail maintained</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Role-based access control active</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">IP address logging enabled</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Automated compliance reports active</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Government standards compliance: 98.5%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}
