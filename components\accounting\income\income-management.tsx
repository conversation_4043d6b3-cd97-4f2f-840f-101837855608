'use client'

import React, { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { 
  Plus, 
  Upload, 
  Download, 
  Trash2,
  FileSpreadsheet,
  AlertTriangle
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useIncomeStore } from "@/lib/stores/enhanced-income-store"
import { IncomeTable } from "./income-table"
import { BulkIncomeUpload } from "./bulk-income-upload"
import { Income } from "@/types/accounting"

interface IncomeManagementProps {
  fiscalYear?: string;
  budgetId?: string;
  onCreateIncome?: () => void;
  onEditIncome?: (income: Income) => void;
}

export function IncomeManagement({ 
  fiscalYear, 
  budgetId, 
  onCreateIncome, 
  onEditIncome 
}: IncomeManagementProps) {
  const [showBulkUpload, setShowBulkUpload] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedIncomeIds, setSelectedIncomeIds] = useState<string[]>([])
  const [incomeToDelete, setIncomeToDelete] = useState<Income | null>(null)
  
  const { toast } = useToast()
  const { deleteIncome, bulkDeleteIncomes, isLoading } = useIncomeStore()

  // Handle single income delete
  const handleDeleteIncome = async (income: Income) => {
    setIncomeToDelete(income)
    setShowDeleteDialog(true)
  }

  // Handle bulk delete
  const handleBulkDelete = async (ids: string[]) => {
    setSelectedIncomeIds(ids)
    setShowDeleteDialog(true)
  }

  // Confirm delete action
  const confirmDelete = async () => {
    try {
      if (incomeToDelete) {
        // Single delete
        await deleteIncome(incomeToDelete.id || incomeToDelete._id)
        toast({
          title: "Income Deleted",
          description: `Income transaction ${incomeToDelete.reference} has been deleted successfully.`,
          variant: "default"
        })
      } else if (selectedIncomeIds.length > 0) {
        // Bulk delete
        const result = await bulkDeleteIncomes(selectedIncomeIds)
        toast({
          title: "Bulk Delete Completed",
          description: `Successfully deleted ${result.deletedCount} income transactions.`,
          variant: "default"
        })
      }
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: error instanceof Error ? error.message : "Failed to delete income transactions",
        variant: "destructive"
      })
    } finally {
      setShowDeleteDialog(false)
      setIncomeToDelete(null)
      setSelectedIncomeIds([])
    }
  }

  // Handle bulk upload completion
  const handleUploadComplete = () => {
    setShowBulkUpload(false)
    toast({
      title: "Upload Completed",
      description: "Income transactions have been imported successfully.",
      variant: "default"
    })
  }

  // Download template
  const handleDownloadTemplate = () => {
    const timestamp = new Date().getTime()
    window.location.href = `/api/accounting/income/template?t=${timestamp}`
    
    toast({
      title: "Template Downloaded",
      description: "The income import template has been downloaded.",
      variant: "default"
    })
  }

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Income Management</h2>
          <p className="text-muted-foreground">
            Manage income transactions, bulk operations, and imports
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleDownloadTemplate}>
            <Download className="mr-2 h-4 w-4" />
            Template
          </Button>
          <Dialog open={showBulkUpload} onOpenChange={setShowBulkUpload}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Upload className="mr-2 h-4 w-4" />
                Bulk Import
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Bulk Income Import</DialogTitle>
                <DialogDescription>
                  Import multiple income transactions from a CSV or Excel file
                </DialogDescription>
              </DialogHeader>
              <BulkIncomeUpload 
                onUploadComplete={handleUploadComplete}
                onClose={() => setShowBulkUpload(false)}
              />
            </DialogContent>
          </Dialog>
          {onCreateIncome && (
            <Button onClick={onCreateIncome}>
              <Plus className="mr-2 h-4 w-4" />
              Add Income
            </Button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="list" className="space-y-4">
        <TabsList>
          <TabsTrigger value="list">Income List</TabsTrigger>
          <TabsTrigger value="bulk">Bulk Operations</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          <IncomeTable
            fiscalYear={fiscalYear}
            budgetId={budgetId}
            onEditIncome={onEditIncome}
            onDeleteIncome={handleDeleteIncome}
            onBulkDelete={handleBulkDelete}
            showBulkActions={true}
          />
        </TabsContent>

        <TabsContent value="bulk" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Bulk Import Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileSpreadsheet className="h-5 w-5" />
                  Bulk Import
                </CardTitle>
                <CardDescription>
                  Import multiple income transactions from CSV or Excel files
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Features:</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Support for CSV and Excel files</li>
                    <li>• Automatic budget integration</li>
                    <li>• Data validation and error reporting</li>
                    <li>• Template with examples and reference data</li>
                  </ul>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" onClick={handleDownloadTemplate}>
                    <Download className="mr-2 h-4 w-4" />
                    Download Template
                  </Button>
                  <Button onClick={() => setShowBulkUpload(true)}>
                    <Upload className="mr-2 h-4 w-4" />
                    Import Data
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Bulk Operations Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trash2 className="h-5 w-5" />
                  Bulk Operations
                </CardTitle>
                <CardDescription>
                  Perform bulk operations on income transactions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Available Operations:</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Bulk delete with permission checks</li>
                    <li>• Status-based filtering</li>
                    <li>• Creator permission validation</li>
                    <li>• Automatic budget cleanup</li>
                  </ul>
                </div>
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
                    <div className="text-sm">
                      <p className="font-medium text-yellow-800">Important:</p>
                      <p className="text-yellow-700">
                        Only draft and pending income transactions can be bulk deleted. 
                        Approved and received transactions require individual review.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              {incomeToDelete ? (
                <>
                  Are you sure you want to delete the income transaction "{incomeToDelete.reference}"? 
                  This action cannot be undone.
                </>
              ) : (
                <>
                  Are you sure you want to delete {selectedIncomeIds.length} selected income transactions? 
                  This action cannot be undone.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isLoading}
            >
              {isLoading ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
