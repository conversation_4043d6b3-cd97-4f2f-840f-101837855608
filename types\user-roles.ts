// Define the roles available in the system
// types/user-roles.ts
export enum UserRole {
  // Admin roles
  SUPER_ADMIN = 'super_admin',
  SYSTEM_ADMIN = 'system_admin',
  ADMIN = 'admin',
  AUDITOR = 'auditor',

  // HR roles
  HR_DIRECTOR = 'hr_director',
  HR_MANAGER = 'hr_manager',
  HR_SPECIALIST = 'hr_specialist',
  HR_OFFICER = 'hr_officer',
  HR_STAFF = 'hr_staff',
  RECRUITER = 'recruiter',
  TRAINING_COORDINATOR = 'training_coordinator',

  // Management roles
  DIRECTOR = 'director',
  MANAGER = 'manager',
  SUPERVISOR = 'supervisor',
  DEPARTMENT_HEAD = 'department_head',
  TEAM_LEADER = 'team_leader',
  PROJECT_MANAGER = 'project_manager',

  // Employee roles
  EMPLOYEE = 'employee',
  CONTRACTOR = 'contractor',
  INTERN = 'intern',

  // Finance roles
  FINANCE_DIRECTOR = 'finance_director',
  FINANCE_MANAGER = 'finance_manager',
  FINANCE_OFFICER = 'finance_officer',
  FINANCE_SPECIALIST = 'finance_specialist',
  ACCOUNTANT = 'accountant',
  BUDGET_ANALYST = 'budget_analyst',
  PAYROLL_MANAGER = 'payroll_manager',
  PAYROLL_OFFICER = 'payroll_officer',
  PAYROLL_SPECIALIST = 'payroll_specialist',

  // Procurement roles
  PROCUREMENT_MANAGER = 'procurement_manager',
  PROCUREMENT_OFFICER = 'procurement_officer',

  // Asset & Inventory roles
  ASSET_MANAGER = 'asset_manager',
  INVENTORY_MANAGER = 'inventory_manager',
  INVENTORY_CLERK = 'inventory_clerk',
  MAINTENANCE_MANAGER = 'maintenance_manager',

  // CRM roles
  SALES_DIRECTOR = 'sales_director',
  SALES_MANAGER = 'sales_manager',
  ACCOUNT_MANAGER = 'account_manager',
  SALES_REPRESENTATIVE = 'sales_representative',
  CUSTOMER_SERVICE_MANAGER = 'customer_service_manager',
  CUSTOMER_SERVICE_REPRESENTATIVE = 'customer_service_representative',
  MARKETING_MANAGER = 'marketing_manager',
  MARKETING_SPECIALIST = 'marketing_specialist'
}

// Define the status options for a user
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending',
  ON_LEAVE = 'on_leave',
  TERMINATED = 'terminated',
  PROBATION = 'probation',
  BLOCKED = 'blocked',      // Temporarily blocked due to suspicious activity
  BANNED = 'banned',        // Permanently banned due to policy violations
  REVOKED = 'revoked',      // Access revoked (e.g., employee left the company)
  LOCKED = 'locked'         // Account locked due to multiple failed login attempts
}
