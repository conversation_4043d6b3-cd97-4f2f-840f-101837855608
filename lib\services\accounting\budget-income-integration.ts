// lib/services/accounting/budget-income-integration.ts
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import { LogCategory } from '@/types/logging';
import { Budget, BudgetCategory, BudgetItem } from '@/models/accounting/Budget';
import mongoose from 'mongoose';

/**
 * Service for integrating income with budget items
 * This complements the existing budgetFundService by also creating BudgetItems
 */
export class BudgetIncomeIntegrationService {
  /**
   * Create BudgetItem for income (optional enhancement)
   * This creates actual budget line items that appear in budget planning
   */
  async createIncomeAsBudgetItem(income: any): Promise<void> {
    try {
      if (!income.appliedToBudget || !income.budget || !income.budgetCategory) {
        logger.info('Income not linked to budget, skipping BudgetItem creation', LogCategory.ACCOUNTING, {
          incomeId: income._id,
          appliedToBudget: income.appliedToBudget,
          hasBudget: !!income.budget,
          hasBudgetCategory: !!income.budgetCategory
        });
        return;
      }

      await connectToDatabase();

      // Check if BudgetItem already exists for this income
      const existingItem = await BudgetItem.findOne({
        name: `Income: ${income.reference}`,
        parentCategory: income.budgetCategory,
        budget: income.budget
      });

      if (existingItem) {
        logger.info('BudgetItem already exists for income', LogCategory.ACCOUNTING, {
          incomeId: income._id,
          budgetItemId: existingItem._id
        });
        return;
      }

      // Create BudgetItem for this income
      const budgetItem = await BudgetItem.create({
        name: `Income: ${income.reference}`,
        description: income.description || `${income.source} income - ${income.reference}`,
        quantity: 1,
        frequency: 1,
        unitCost: income.amount,
        amount: income.amount,
        parentCategory: income.budgetCategory,
        parentSubcategory: income.budgetSubcategory,
        budget: income.budget,
      });

      // Ensure budget category is in budget.categories array
      await Budget.findByIdAndUpdate(income.budget, {
        $addToSet: { categories: income.budgetCategory }
      });

      logger.info('Created BudgetItem for income', LogCategory.ACCOUNTING, {
        incomeId: income._id,
        budgetItemId: budgetItem._id,
        budgetId: income.budget,
        categoryId: income.budgetCategory,
        amount: income.amount
      });

    } catch (error) {
      logger.error('Error creating BudgetItem for income', LogCategory.ACCOUNTING, error);
      // Don't throw error - this is an optional enhancement
    }
  }

  /**
   * Update BudgetItem when income changes
   */
  async updateIncomeAsBudgetItem(income: any): Promise<void> {
    try {
      if (!income.appliedToBudget || !income.budget || !income.budgetCategory) {
        return;
      }

      await connectToDatabase();

      // Find existing BudgetItem for this income
      const budgetItem = await BudgetItem.findOne({
        name: `Income: ${income.reference}`,
        parentCategory: income.budgetCategory,
        budget: income.budget
      });

      if (budgetItem) {
        // Update the BudgetItem
        budgetItem.description = income.description || `${income.source} income - ${income.reference}`;
        budgetItem.unitCost = income.amount;
        budgetItem.amount = income.amount;
        
        await budgetItem.save();

        logger.info('Updated BudgetItem for income', LogCategory.ACCOUNTING, {
          incomeId: income._id,
          budgetItemId: budgetItem._id,
          newAmount: income.amount
        });
      }

    } catch (error) {
      logger.error('Error updating BudgetItem for income', LogCategory.ACCOUNTING, error);
      // Don't throw error - this is an optional enhancement
    }
  }

  /**
   * Remove BudgetItem when income is deleted
   */
  async removeIncomeAsBudgetItem(income: any): Promise<void> {
    try {
      if (!income.budget || !income.budgetCategory) {
        return;
      }

      await connectToDatabase();

      // Find and remove BudgetItem for this income
      const result = await BudgetItem.deleteOne({
        name: `Income: ${income.reference}`,
        parentCategory: income.budgetCategory,
        budget: income.budget
      });

      if (result.deletedCount > 0) {
        logger.info('Removed BudgetItem for income', LogCategory.ACCOUNTING, {
          incomeId: income._id,
          budgetId: income.budget,
          categoryId: income.budgetCategory
        });
      }

    } catch (error) {
      logger.error('Error removing BudgetItem for income', LogCategory.ACCOUNTING, error);
      // Don't throw error - this is an optional enhancement
    }
  }

  /**
   * Verify budget category items integration
   * This method can be used to test that the budget fund service works correctly
   */
  async verifyBudgetCategoryItemsIntegration(budgetId: string): Promise<any> {
    try {
      await connectToDatabase();

      const budget = await Budget.findById(budgetId).populate('categories');
      if (!budget) {
        throw new Error(`Budget ${budgetId} not found`);
      }

      const categories = await BudgetCategory.find({ budget: budgetId });
      
      const verification = {
        budgetId,
        budgetName: budget.name,
        categoriesCount: categories.length,
        categories: categories.map(cat => ({
          id: cat._id,
          name: cat.name,
          type: cat.type,
          itemsCount: cat.items ? cat.items.length : 0,
          items: cat.items || [],
          actualAmount: cat.actualAmount,
          budgetedAmount: cat.budgetedAmount
        }))
      };

      logger.info('Budget category items verification', LogCategory.ACCOUNTING, verification);
      return verification;

    } catch (error) {
      logger.error('Error verifying budget category items integration', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }
}

// Export singleton instance
export const budgetIncomeIntegrationService = new BudgetIncomeIntegrationService();
export default budgetIncomeIntegrationService;
