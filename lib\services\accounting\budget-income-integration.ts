// lib/services/accounting/budget-income-integration.ts
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';

import { Budget, BudgetCategory, BudgetItem } from '@/models/accounting/Budget';
import mongoose from 'mongoose';

/**
 * Service for integrating income with budget items
 * This complements the existing budgetFundService by also creating BudgetItems
 */
export class BudgetIncomeIntegrationService {
  /**
   * Create BudgetItem for income (optional enhancement) - OPTIMIZED
   * This creates actual budget line items that appear in budget planning
   */
  async createIncomeAsBudgetItem(income: any): Promise<void> {
    try {
      if (!income.appliedToBudget || !income.budget || !income.budgetCategory) {
        return; // Skip silently for better performance
      }

      await connectToDatabase();

      // Use upsert to avoid checking for existing item first (more efficient)
      const budgetItem = await BudgetItem.findOneAndUpdate(
        {
          name: `Income: ${income.reference}`,
          parentCategory: income.budgetCategory,
          budget: income.budget
        },
        {
          $set: {
            description: income.description || `${income.source} income - ${income.reference}`,
            quantity: 1,
            frequency: 1,
            unitCost: income.amount,
            amount: income.amount,
            parentCategory: income.budgetCategory,
            parentSubcategory: income.budgetSubcategory,
            budget: income.budget,
          }
        },
        {
          upsert: true, // Create if doesn't exist, update if exists
          new: true,    // Return the updated document
          lean: true    // Return plain object for better performance
        }
      );

      // Use Promise.all for parallel operations (more efficient)
      await Promise.all([
        // Ensure budget category is in budget.categories array
        Budget.findByIdAndUpdate(income.budget, {
          $addToSet: { categories: income.budgetCategory }
        }, { lean: true })
      ]);

      // Log success (non-blocking)
      if (budgetItem && typeof budgetItem === 'object' && '_id' in budgetItem) {
        logger.info('Created/Updated BudgetItem for income', {
          incomeId: income._id,
          budgetItemId: (budgetItem as any)._id,
          budgetId: income.budget,
          categoryId: income.budgetCategory,
          amount: income.amount
        });
      }

    } catch (error) {
      // Log error but don't throw - this is an optional enhancement
      logger.error('Error creating BudgetItem for income', error);
    }
  }

  /**
   * Update BudgetItem when income changes - OPTIMIZED
   */
  async updateIncomeAsBudgetItem(income: any): Promise<void> {
    try {
      if (!income.appliedToBudget || !income.budget || !income.budgetCategory) {
        return;
      }

      await connectToDatabase();

      // Use findOneAndUpdate for better performance (single operation)
      const budgetItem = await BudgetItem.findOneAndUpdate(
        {
          name: `Income: ${income.reference}`,
          parentCategory: income.budgetCategory,
          budget: income.budget
        },
        {
          $set: {
            description: income.description || `${income.source} income - ${income.reference}`,
            unitCost: income.amount,
            amount: income.amount
          }
        },
        {
          new: true,
          lean: true // Better performance
        }
      );

      if (budgetItem) {
        logger.info('Updated BudgetItem for income', {
          incomeId: income._id,
          budgetItemId: (budgetItem as any)._id,
          newAmount: income.amount
        });
      }

    } catch (error) {
      logger.error('Error updating BudgetItem for income', error);
      // Don't throw error - this is an optional enhancement
    }
  }

  /**
   * Remove BudgetItem when income is deleted - OPTIMIZED
   */
  async removeIncomeAsBudgetItem(income: any): Promise<void> {
    try {
      if (!income.budget || !income.budgetCategory) {
        return;
      }

      await connectToDatabase();

      // Use deleteOne with lean option for better performance
      const result = await BudgetItem.deleteOne({
        name: `Income: ${income.reference}`,
        parentCategory: income.budgetCategory,
        budget: income.budget
      }).lean();

      if (result.deletedCount > 0) {
        logger.info('Removed BudgetItem for income', {
          incomeId: income._id,
          budgetId: income.budget,
          categoryId: income.budgetCategory
        });
      }

    } catch (error) {
      logger.error('Error removing BudgetItem for income', error);
      // Don't throw error - this is an optional enhancement
    }
  }

  /**
   * Verify budget category items integration
   * This method can be used to test that the budget fund service works correctly
   */
  async verifyBudgetCategoryItemsIntegration(budgetId: string): Promise<any> {
    try {
      await connectToDatabase();

      const budget = await Budget.findById(budgetId).populate('categories');
      if (!budget) {
        throw new Error(`Budget ${budgetId} not found`);
      }

      const categories = await BudgetCategory.find({ budget: budgetId });
      
      const verification = {
        budgetId,
        budgetName: budget.name,
        categoriesCount: categories.length,
        categories: categories.map(cat => ({
          id: cat._id,
          name: cat.name,
          type: cat.type,
          itemsCount: cat.items ? cat.items.length : 0,
          items: cat.items || [],
          actualAmount: cat.actualAmount,
          budgetedAmount: cat.budgetedAmount
        }))
      };

      logger.info('Budget category items verification', verification);
      return verification;

    } catch (error) {
      logger.error('Error verifying budget category items integration', error);
      throw error;
    }
  }
}

// Export singleton instance
export const budgetIncomeIntegrationService = new BudgetIncomeIntegrationService();
export default budgetIncomeIntegrationService;
