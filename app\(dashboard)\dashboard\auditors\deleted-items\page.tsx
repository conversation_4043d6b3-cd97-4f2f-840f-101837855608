// app/(dashboard)/dashboard/auditors/deleted-items/page.tsx
import { Metadata } from "next"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { 
  Trash2, 
  Search, 
  Filter, 
  Download, 
  Eye,
  RotateCcw,
  Calendar,
  User,
  FileText,
  Shield,
  Clock
} from "lucide-react"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Deleted Items - Auditors",
  description: "View and manage all deleted items in the audit trail",
}

export default function DeletedItemsPage() {
  // Mock data - replace with real data from API
  const deletedItems = [
    {
      id: "del_001",
      originalId: "inc_12345",
      itemType: "Income Transaction",
      originalData: {
        reference: "INC-2025-001",
        amount: 150000,
        source: "Government Grant"
      },
      deletedBy: {
        name: "John Doe",
        email: "<EMAIL>",
        role: "Finance Manager"
      },
      deletionDate: "2025-01-15T10:30:00Z",
      deletionReason: "Duplicate entry created during data import process",
      deletionType: "single",
      recoveryDeadline: "2025-04-15T10:30:00Z",
      status: "Active",
      complianceFlags: ["High Value", "Financial Record"],
      auditRecordId: "audit_001"
    },
    {
      id: "del_002",
      originalId: "exp_67890",
      itemType: "Expenditure Record",
      originalData: {
        expenditureNumber: "EXP-2025-002",
        amount: 75000,
        title: "Office Supplies"
      },
      deletedBy: {
        name: "Jane Smith",
        email: "<EMAIL>",
        role: "Accountant"
      },
      deletionDate: "2025-01-14T14:20:00Z",
      deletionReason: "Data correction requested by supervisor on 2025-01-14",
      deletionType: "bulk",
      recoveryDeadline: "2025-04-14T14:20:00Z",
      status: "Under Review",
      complianceFlags: ["Pending Approval"],
      auditRecordId: "audit_002"
    },
    {
      id: "del_003",
      originalId: "emp_11111",
      itemType: "Employee Record",
      originalData: {
        employeeNumber: "TCM-2024-001",
        firstName: "Michael",
        lastName: "Johnson"
      },
      deletedBy: {
        name: "Sarah Wilson",
        email: "<EMAIL>",
        role: "HR Manager"
      },
      deletionDate: "2025-01-13T09:15:00Z",
      deletionReason: "Employee left organization - contract terminated",
      deletionType: "single",
      recoveryDeadline: "2025-04-13T09:15:00Z",
      status: "Expired",
      complianceFlags: ["HR Record", "Personal Data"],
      auditRecordId: "audit_003"
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-700">Active</Badge>
      case "Under Review":
        return <Badge className="bg-yellow-100 text-yellow-700">Under Review</Badge>
      case "Expired":
        return <Badge className="bg-red-100 text-red-700">Expired</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getDaysUntilExpiry = (deadline: string) => {
    const now = new Date()
    const expiry = new Date(deadline)
    const diffTime = expiry.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Deleted Items"
        text="View and manage all deleted items in the audit trail"
      >
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Link href="/dashboard/auditors/recovery-center">
            <Button size="sm">
              <RotateCcw className="h-4 w-4 mr-2" />
              Recovery Center
            </Button>
          </Link>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filters</CardTitle>
            <CardDescription>
              Filter deleted items by type, date, user, or status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search items..." className="pl-9" />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Item Type</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="income">Income Transactions</SelectItem>
                    <SelectItem value="expenditure">Expenditure Records</SelectItem>
                    <SelectItem value="employee">Employee Records</SelectItem>
                    <SelectItem value="budget">Budget Items</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="under-review">Under Review</SelectItem>
                    <SelectItem value="expired">Expired</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Date Range</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Last 30 days" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">Last 7 days</SelectItem>
                    <SelectItem value="30">Last 30 days</SelectItem>
                    <SelectItem value="90">Last 90 days</SelectItem>
                    <SelectItem value="365">Last year</SelectItem>
                    <SelectItem value="all">All time</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Deleted Items Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-600" />
              Deleted Items ({deletedItems.length})
            </CardTitle>
            <CardDescription>
              Complete audit trail of all deleted items with government compliance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item Details</TableHead>
                    <TableHead>Deleted By</TableHead>
                    <TableHead>Deletion Date</TableHead>
                    <TableHead>Recovery Status</TableHead>
                    <TableHead>Compliance</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {deletedItems.map((item) => {
                    const daysUntilExpiry = getDaysUntilExpiry(item.recoveryDeadline)
                    return (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div className="space-y-1">
                            <p className="font-medium text-sm">{item.itemType}</p>
                            <p className="text-xs text-muted-foreground">
                              ID: {item.originalId}
                            </p>
                            {item.originalData && (
                              <p className="text-xs text-gray-600">
                                {Object.entries(item.originalData).slice(0, 2).map(([key, value]) => 
                                  `${key}: ${value}`
                                ).join(', ')}
                              </p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <p className="font-medium text-sm">{item.deletedBy.name}</p>
                            <p className="text-xs text-muted-foreground">{item.deletedBy.role}</p>
                            <p className="text-xs text-gray-600">{item.deletedBy.email}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <p className="text-sm">{formatDate(item.deletionDate)}</p>
                            <Badge variant="outline" className="text-xs">
                              {item.deletionType}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {getStatusBadge(item.status)}
                            {item.status === "Active" && (
                              <p className="text-xs text-muted-foreground">
                                {daysUntilExpiry > 0 ? `${daysUntilExpiry} days left` : "Expired"}
                              </p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {item.complianceFlags.map((flag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs mr-1">
                                {flag}
                              </Badge>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            {item.status === "Active" && daysUntilExpiry > 0 && (
                              <Button variant="ghost" size="sm">
                                <RotateCcw className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Compliance Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-green-600" />
              Audit Compliance Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                <Clock className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-sm">7-Year Retention</p>
                  <p className="text-xs text-muted-foreground">Government standard compliance</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                <Shield className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium text-sm">Secure Storage</p>
                  <p className="text-xs text-muted-foreground">Encrypted audit trail</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                <RotateCcw className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="font-medium text-sm">90-Day Recovery</p>
                  <p className="text-xs text-muted-foreground">Recovery window available</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}
