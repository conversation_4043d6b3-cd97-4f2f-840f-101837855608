import mongoose, { Schema, Document } from 'mongoose';

export interface IBudgetItem extends Document {
  name: string;
  description?: string;
  quantity: number;
  frequency: number;
  unitCost: number;
  amount: number;
  parentCategory?: mongoose.Types.ObjectId;
  parentSubcategory?: mongoose.Types.ObjectId;
  budget: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

export interface IBudgetSubcategory extends Document {
  name: string;
  description?: string;
  parentCategory: mongoose.Types.ObjectId;
  budget: mongoose.Types.ObjectId;
  total: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface IBudgetCategory extends Document {
  name: string;
  description?: string;
  type: 'income' | 'expense';
  budget: mongoose.Types.ObjectId;
  total: number;
  budgetedAmount: number;
  actualAmount: number;
  lastActualUpdateDate?: Date;

  // ✅ ADD MISSING FIELD for budget fund service integration
  items: Array<{
    sourceId: mongoose.Types.ObjectId;
    sourceType: 'income' | 'expense';
    description: string;
    amount: number;
    status: string;
    reference: string;
    date: Date;
    contributionType: 'projected' | 'expected' | 'actual';
  }>;

  createdAt: Date;
  updatedAt: Date;
}

export interface IBudget extends Document {
  name: string;
  description?: string;
  fiscalYear: string;
  startDate: Date;
  endDate: Date;
  status: 'draft' | 'pending_approval' | 'approved' | 'rejected' | 'active' | 'closed';
  totalIncome: number;
  totalExpense: number;
  totalBudgeted: number;
  totalActualIncome: number;
  totalActualExpense: number;
  lastActualUpdateDate?: Date;
  categories: mongoose.Types.ObjectId[];
  createdBy: mongoose.Types.ObjectId;
  approvedBy?: mongoose.Types.ObjectId;
  approvedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const BudgetItemSchema: Schema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    quantity: {
      type: Number,
      required: true,
      default: 1,
    },
    frequency: {
      type: Number,
      required: true,
      default: 1,
    },
    unitCost: {
      type: Number,
      required: true,
    },
    amount: {
      type: Number,
      required: true,
    },
    parentCategory: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BudgetCategory',
    },
    parentSubcategory: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BudgetSubcategory',
    },
    budget: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Budget',
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

const BudgetSubcategorySchema: Schema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    parentCategory: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BudgetCategory',
      required: true,
    },
    budget: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Budget',
      required: true,
    },
    total: {
      type: Number,
      required: true,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
);

const BudgetCategorySchema: Schema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    type: {
      type: String,
      required: true,
      enum: ['income', 'expense'],
    },
    budget: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Budget',
      required: true,
    },
    total: {
      type: Number,
      required: true,
      default: 0,
    },
    budgetedAmount: {
      type: Number,
      required: true,
      default: 0,
    },
    actualAmount: {
      type: Number,
      required: true,
      default: 0,
    },
    lastActualUpdateDate: {
      type: Date,
    },

    // ✅ ADD MISSING FIELD for budget fund service integration
    items: [{
      sourceId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true
      },
      sourceType: {
        type: String,
        enum: ['income', 'expense'],
        required: true
      },
      description: {
        type: String,
        required: true
      },
      amount: {
        type: Number,
        required: true
      },
      status: {
        type: String,
        required: true
      },
      reference: {
        type: String,
        required: true
      },
      date: {
        type: Date,
        required: true
      },
      contributionType: {
        type: String,
        enum: ['projected', 'expected', 'actual'],
        required: true
      }
    }],
  },
  {
    timestamps: true,
  }
);

const BudgetSchema: Schema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    fiscalYear: {
      type: String,
      required: true,
      trim: true,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
    status: {
      type: String,
      required: true,
      enum: ['draft', 'pending_approval', 'approved', 'rejected', 'active', 'closed'],
      default: 'draft',
    },
    totalIncome: {
      type: Number,
      required: true,
      default: 0,
    },
    totalExpense: {
      type: Number,
      required: true,
      default: 0,
    },
    totalBudgeted: {
      type: Number,
      required: true,
      default: 0,
    },
    totalActualIncome: {
      type: Number,
      required: true,
      default: 0,
    },
    totalActualExpense: {
      type: Number,
      required: true,
      default: 0,
    },
    lastActualUpdateDate: {
      type: Date,
    },
    categories: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BudgetCategory',
    }],
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    approvedAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      }
    },
    toObject: {
      transform: function(doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      }
    }
  }
);

// Create indexes for better query performance
BudgetItemSchema.index({ budget: 1 });
BudgetItemSchema.index({ parentCategory: 1 });
BudgetItemSchema.index({ parentSubcategory: 1 });

BudgetSubcategorySchema.index({ budget: 1 });
BudgetSubcategorySchema.index({ parentCategory: 1 });

BudgetCategorySchema.index({ budget: 1 });
BudgetCategorySchema.index({ type: 1 });

BudgetSchema.index({ fiscalYear: 1 });
BudgetSchema.index({ status: 1 });
BudgetSchema.index({ createdBy: 1 });

// Method to update budget actual amounts when a transaction is created, modified, or deleted
BudgetSchema.methods.updateActualAmounts = async function() {
  const Income = mongoose.models.Income;
  const Expense = mongoose.models.Expense;

  // Calculate total actual income
  const incomeResult = await Income.aggregate([
    {
      $match: {
        fiscalYear: this.fiscalYear,
        status: 'received',
        date: { $gte: this.startDate, $lte: this.endDate }
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$amount' }
      }
    }
  ]);

  // Calculate total actual expense
  const expenseResult = await Expense.aggregate([
    {
      $match: {
        fiscalYear: this.fiscalYear,
        status: 'paid',
        date: { $gte: this.startDate, $lte: this.endDate }
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$amount' }
      }
    }
  ]);

  // Update budget totals
  this.totalActualIncome = incomeResult.length > 0 ? incomeResult[0].total : 0;
  this.totalActualExpense = expenseResult.length > 0 ? expenseResult[0].total : 0;
  this.lastActualUpdateDate = new Date();

  // Save the updated budget
  return this.save();
};

// Method to update category actual amounts
BudgetCategorySchema.methods.updateActualAmount = async function() {
  const Income = mongoose.models.Income;
  const Expense = mongoose.models.Expense;

  if (this.type === 'income') {
    // Calculate actual income for this category
    const incomeResult = await Income.aggregate([
      {
        $match: {
          budgetCategory: this._id,
          status: 'received'
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' }
        }
      }
    ]);

    this.actualAmount = incomeResult.length > 0 ? incomeResult[0].total : 0;
  } else {
    // Calculate actual expense for this category
    const expenseResult = await Expense.aggregate([
      {
        $match: {
          budgetCategory: this._id,
          status: 'paid'
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' }
        }
      }
    ]);

    this.actualAmount = expenseResult.length > 0 ? expenseResult[0].total : 0;
  }

  this.lastActualUpdateDate = new Date();

  // Save the updated category
  return this.save();
};

export const BudgetItem = mongoose.models.BudgetItem || mongoose.model<IBudgetItem>('BudgetItem', BudgetItemSchema);
export const BudgetSubcategory = mongoose.models.BudgetSubcategory || mongoose.model<IBudgetSubcategory>('BudgetSubcategory', BudgetSubcategorySchema);
export const BudgetCategory = mongoose.models.BudgetCategory || mongoose.model<IBudgetCategory>('BudgetCategory', BudgetCategorySchema);
export const Budget = mongoose.models.Budget || mongoose.model<IBudget>('Budget', BudgetSchema);

// Add default export for backward compatibility
const BudgetModel = mongoose.models.Budget || mongoose.model<IBudget>('Budget', BudgetSchema);
export default BudgetModel;
