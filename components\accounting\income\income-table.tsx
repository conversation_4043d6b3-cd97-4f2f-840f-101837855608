// components/accounting/income/income-table.tsx
'use client';

import React, { useState } from 'react';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { format } from "date-fns";
import {
  ArrowUpDown,
  ChevronDown,
  Download,
  MoreHorizontal,
  FileText,
  Edit,
  Trash2,
  Eye,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Skeleton } from "@/components/ui/skeleton";
import { useIncome } from "@/lib/hooks/accounting/use-income";
import { Income, Budget } from "@/types/accounting";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useBudget } from "@/lib/hooks/accounting/use-budget";
import { useIncomeStore } from "@/lib/stores/enhanced-income-store";

// Extended Income interface for table display with populated budget names
interface IncomeWithBudgetNames extends Income {
  budgetName?: string;
  budgetCategoryName?: string;
  budgetSubcategoryName?: string;
}

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

// Define the columns for the income table
const createColumns = (
  onEditIncome?: (income: Income) => void,
  onDeleteIncome?: (income: Income) => void
): ColumnDef<IncomeWithBudgetNames>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "date",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date = row.getValue("date") as Date;
      return <div>{format(new Date(date), "PPP")}</div>;
    },
  },
  {
    accessorKey: "reference",
    header: "Reference",
    cell: ({ row }) => <div>{row.getValue("reference")}</div>,
  },
  {
    accessorKey: "source",
    header: "Source",
    cell: ({ row }) => {
      const source = row.getValue("source") as string;
      const subSource = row.original.subSource;
      return (
        <div>
          <div className="font-medium">{source.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</div>
          {subSource && (
            <div className="text-xs text-muted-foreground">{subSource}</div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => {
      const description = row.getValue("description") as string | undefined;
      return (
        <div className="max-w-[300px] truncate" title={description}>
          {description || "—"}
        </div>
      );
    },
  },
  {
    accessorKey: "budget",
    header: "Budget",
    cell: ({ row }) => {
      const budgetId = row.original.budget;
      const budgetCategory = row.original.budgetCategory;

      if (!budgetId || !budgetCategory) {
        return <div className="text-muted-foreground text-xs">Not budgeted</div>;
      }

      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center">
                <Badge variant="outline" className="bg-blue-50">
                  <FileText className="h-3 w-3 mr-1 text-blue-500" />
                  <span className="truncate max-w-[120px]">
                    {row.original.budgetCategoryName || "Budget Category"}
                  </span>
                </Badge>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className="space-y-1">
                <p className="font-medium">{row.original.budgetName || "Budget"}</p>
                <p className="text-xs text-muted-foreground">
                  Category: {row.original.budgetCategoryName || "Budget Category"}
                </p>
                {row.original.budgetSubcategoryName && (
                  <p className="text-xs text-muted-foreground">
                    Subcategory: {row.original.budgetSubcategoryName}
                  </p>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    },
  },
  {
    accessorKey: "amount",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="justify-end"
        >
          Amount
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("amount"));
      return (
        <div className="text-right font-medium">{formatCurrency(amount)}</div>
      );
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;

      const statusMap: Record<string, { label: string; variant: "default" | "outline" | "secondary" | "destructive" }> = {
        draft: { label: "Draft", variant: "secondary" },
        pending_approval: { label: "Pending Approval", variant: "default" },
        approved: { label: "Approved", variant: "default" },
        received: { label: "Received", variant: "default" },
        rejected: { label: "Rejected", variant: "destructive" },
        cancelled: { label: "Cancelled", variant: "destructive" },
      };

      const { label, variant } = statusMap[status] || { label: status, variant: "default" };

      return (
        <Badge variant={variant}>
          {label}
        </Badge>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const income = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(income.id)}
            >
              Copy ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Eye className="mr-2 h-4 w-4" />
              View details
            </DropdownMenuItem>
            {onEditIncome && (
              <DropdownMenuItem onClick={() => onEditIncome(income)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit income
              </DropdownMenuItem>
            )}
            {income.status === 'pending' && (
              <DropdownMenuItem>Mark as received</DropdownMenuItem>
            )}
            {onDeleteIncome && income.status !== 'cancelled' && (
              <DropdownMenuItem
                className="text-destructive"
                onClick={() => onDeleteIncome(income)}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

interface IncomeTableProps {
  title?: string;
  description?: string;
  showFilters?: boolean;
  fiscalYear?: string;
  budgetId?: string;
  limit?: number;
  onEditIncome?: (income: Income) => void;
  onDeleteIncome?: (income: Income) => void;
  onBulkDelete?: (ids: string[]) => void;
  showBulkActions?: boolean;
}

export function IncomeTable({
  title = "Income Transactions",
  description = "View and manage all income transactions for the Teachers Council of Malawi.",
  showFilters = true,
  fiscalYear,
  budgetId,
  limit = 10,
  onEditIncome,
  onDeleteIncome,
  onBulkDelete,
  showBulkActions = true
}: IncomeTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [page, setPage] = useState(1);
  const [selectedBudget, setSelectedBudget] = useState<string | undefined>(budgetId);
  const [selectedFiscalYear, setSelectedFiscalYear] = useState<string | undefined>(fiscalYear);
  const [selectedStatus, setSelectedStatus] = useState<string | undefined>();

  // Get income data using the custom hook
  const { useIncomeList } = useIncome();
  const { data, isLoading, error } = useIncomeList(page, limit, {
    fiscalYear: selectedFiscalYear === "all" ? undefined : selectedFiscalYear,
    budget: selectedBudget === "all" ? undefined : selectedBudget,
    status: selectedStatus === "all" ? undefined : selectedStatus
  });

  // Handle bulk delete
  const handleBulkDelete = () => {
    const selectedRows = table.getFilteredSelectedRowModel().rows;
    const selectedIds = selectedRows.map(row => row.original.id || row.original._id);

    if (selectedIds.length > 0 && onBulkDelete) {
      onBulkDelete(selectedIds);
      setRowSelection({});
    }
  };

  // Get budgets for filtering
  const { activeBudgets } = useBudget();

  // Get fiscal years for filtering from enhanced income store
  const { getActiveFiscalYears, getCurrentFiscalYear } = useIncomeStore();
  const fiscalYears = getActiveFiscalYears().length > 0
    ? getActiveFiscalYears().map(fy => fy.year)
    : [getCurrentFiscalYear()]; // Fallback to current fiscal year

  // Create columns with callbacks
  const columns = createColumns(onEditIncome, onDeleteIncome);

  // Create table instance
  const table = useReactTable({
    data: (data?.incomes || []) as IncomeWithBudgetNames[],
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-4">
          {showFilters && (
            <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 sm:space-x-2">
              <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2">
                <Input
                  placeholder="Filter by reference..."
                  value={(table.getColumn("reference")?.getFilterValue() as string) ?? ""}
                  onChange={(event) =>
                    table.getColumn("reference")?.setFilterValue(event.target.value)
                  }
                  className="max-w-sm"
                />

                <Select
                  value={selectedFiscalYear}
                  onValueChange={setSelectedFiscalYear}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Fiscal Year" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Fiscal Years</SelectItem>
                    {fiscalYears.map((year) => (
                      <SelectItem key={year} value={year}>{year}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={selectedBudget}
                  onValueChange={setSelectedBudget}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Budget" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Budgets</SelectItem>
                    {activeBudgets?.map((budget: Budget) => (
                      <SelectItem key={budget.id} value={budget.id}>{budget.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={selectedStatus}
                  onValueChange={setSelectedStatus}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="approved,received">Approved & Received</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="pending_approval">Pending Approval</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="received">Received</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-9 gap-1">
                      <ChevronDown className="h-4 w-4" />
                      <span>Columns</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {table
                      .getAllColumns()
                      .filter((column) => column.getCanHide())
                      .map((column) => {
                        return (
                          <DropdownMenuCheckboxItem
                            key={column.id}
                            className="capitalize"
                            checked={column.getIsVisible()}
                            onCheckedChange={(value) =>
                              column.toggleVisibility(!!value)
                            }
                          >
                            {column.id}
                          </DropdownMenuCheckboxItem>
                        );
                      })}
                  </DropdownMenuContent>
                </DropdownMenu>
                <Button variant="outline" size="sm" className="h-9 gap-1">
                  <Download className="h-4 w-4" />
                  <span className="hidden sm:inline">Export</span>
                </Button>
              </div>
            </div>
          )}

          {/* Bulk Actions */}
          {showBulkActions && table.getFilteredSelectedRowModel().rows.length > 0 && (
            <div className="flex items-center justify-between p-4 bg-muted/50 rounded-md">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">
                  {table.getFilteredSelectedRowModel().rows.length} of{" "}
                  {table.getFilteredRowModel().rows.length} row(s) selected
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setRowSelection({})}
                >
                  Clear Selection
                </Button>
                {onBulkDelete && (
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleBulkDelete}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Selected ({table.getFilteredSelectedRowModel().rows.length})
                  </Button>
                )}
              </div>
            </div>
          )}

          {isLoading ? (
            <div className="space-y-2">
              {Array(5).fill(0).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : error ? (
            <div className="py-6 text-center text-muted-foreground">
              <p>Error loading income data. Please try again.</p>
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    {table.getHeaderGroups().map((headerGroup) => (
                      <TableRow key={headerGroup.id}>
                        {headerGroup.headers.map((header) => {
                          return (
                            <TableHead key={header.id}>
                              {header.isPlaceholder
                                ? null
                                : flexRender(
                                    header.column.columnDef.header,
                                    header.getContext()
                                  )}
                            </TableHead>
                          );
                        })}
                      </TableRow>
                    ))}
                  </TableHeader>
                  <TableBody>
                    {table.getRowModel().rows?.length ? (
                      table.getRowModel().rows.map((row) => (
                        <TableRow
                          key={row.id}
                          data-state={row.getIsSelected() && "selected"}
                        >
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id}>
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell
                          colSpan={columns.length}
                          className="h-24 text-center"
                        >
                          No income transactions found.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>

              <div className="flex items-center justify-between space-x-2">
                <div className="text-sm text-muted-foreground">
                  Showing {table.getFilteredRowModel().rows.length} of {data?.pagination?.totalCount || 0} income transactions
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page > 1 ? page - 1 : 1)}
                    disabled={page === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page + 1)}
                    disabled={!data?.pagination?.hasNextPage}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
