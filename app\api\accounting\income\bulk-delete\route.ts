// app/api/accounting/income/bulk-delete/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import Income from '@/models/accounting/Income';
import { AuditDeletionService } from '@/lib/services/audit/audit-deletion-service';
import { budgetIncomeIntegrationService } from '@/lib/services/accounting/budget-income-integration';
import { ErrorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';

export const runtime = 'nodejs';

/**
 * POST /api/accounting/income/bulk-delete
 * Bulk delete income transactions
 */
export async function POST(req: NextRequest) {
  const errorService = ErrorService.getInstance();

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      const error = errorService.createError(
        ErrorType.AUTHENTICATION,
        'INCOME_BULK_DELETE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to delete income transactions',
        {
          endpoint: '/api/accounting/income/bulk-delete',
          method: 'POST'
        },
        ErrorSeverity.MEDIUM,
        'Unauthenticated user attempted to access income bulk delete endpoint'
      );
      return NextResponse.json({ error: error.userMessage }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      const error = errorService.createError(
        ErrorType.FORBIDDEN,
        'INCOME_BULK_DELETE_FORBIDDEN',
        'Insufficient permissions for bulk deletion',
        'You do not have permission to delete income transactions',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: '/api/accounting/income/bulk-delete',
          method: 'POST'
        },
        ErrorSeverity.HIGH,
        'User without proper permissions attempted income bulk deletion'
      );
      return NextResponse.json({ error: error.userMessage }, { status: 403 });
    }

    // Get request body
    const body = await req.json();
    const { ids, deletionReason } = body;

    // Get client context for audit trail
    const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
    const userAgent = req.headers.get('user-agent') || 'unknown';

    // Prepare audit context
    const auditContext = {
      deletedBy: user.id,
      deletionReason,
      deletionType: 'bulk' as const,
      userInfo: {
        id: user.id,
        name: `${user.firstName} ${user.lastName}`,
        email: user.email,
        role: user.role
      },
      context: {
        ipAddress: clientIP,
        userAgent: userAgent,
        sessionId: req.headers.get('x-session-id') || undefined
      }
    };

    // Define validation rules for income deletion
    const validationRules = [
      {
        field: 'status',
        disallowedValues: ['received', 'approved'],
        errorMessage: 'Cannot delete income with received or approved status'
      }
    ];

    // Define permission check
    const permissionCheck = {
      isAdmin: hasRequiredPermissions(user, [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]),
      userId: user.id,
      checkOwnership: true,
      ownershipField: 'createdBy'
    };

    // Perform audit deletion using the service
    const result = await AuditDeletionService.performAuditDeletion(
      Income,
      ids,
      auditContext,
      {
        validationRules,
        permissionCheck,
        populateFields: ['createdBy'],
        beforeDelete: async (incomes) => {
          // Remove budget integrations before deletion
          const budgetCleanupPromises = incomes
            .filter(income => (income as any).appliedToBudget)
            .map(income =>
              budgetIncomeIntegrationService.removeIncomeAsBudgetItem(income as any)
                .catch(error => {
                  console.error('Error removing budget integration:', error);
                })
            );

          await Promise.allSettled(budgetCleanupPromises);
        }
      }
    );

    return NextResponse.json({
      success: result.success,
      message: `Successfully moved ${result.deletedCount} income transactions to audit trail`,
      deletedCount: result.deletedCount,
      auditRecordsCreated: result.auditRecordsCreated,
      requestedCount: ids.length,
      details: {
        deletedIds: result.deletedIds,
        auditRecordIds: result.auditRecordIds,
        deletionReason: result.details.deletionReason,
        auditCompliance: result.details.auditCompliance
      }
    });

  } catch (error: unknown) {
    const systemError = errorService.createError(
      ErrorType.SYSTEM,
      'INCOME_BULK_DELETE_SYSTEM_ERROR',
      'System error during income bulk deletion',
      error instanceof Error ? error.message : 'An unexpected error occurred during bulk deletion',
      {
        endpoint: '/api/accounting/income/bulk-delete',
        method: 'POST',
        additionalData: { originalError: error instanceof Error ? error.message : 'Unknown error' }
      },
      ErrorSeverity.HIGH,
      'System error occurred during income bulk deletion operation',
      [
        'Please try again in a few moments',
        'If the problem persists, contact system administrator',
        'Check your network connection and try again'
      ]
    );

    return NextResponse.json(
      { error: systemError.userMessage },
      { status: 500 }
    );
  }
}
