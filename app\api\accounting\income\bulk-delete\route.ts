// app/api/accounting/income/bulk-delete/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import Income from '@/models/accounting/Income';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { budgetIncomeIntegrationService } from '@/lib/services/accounting/budget-income-integration';

export const runtime = 'nodejs';

/**
 * POST /api/accounting/income/bulk-delete
 * Bulk delete income transactions
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();
    const { ids } = body;

    // Validate IDs
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'No income IDs provided' },
        { status: 400 }
      );
    }

    // Validate that all IDs are valid MongoDB ObjectIds
    const validIds = ids.filter((id: any) => {
      return typeof id === 'string' && id.match(/^[0-9a-fA-F]{24}$/);
    });

    if (validIds.length !== ids.length) {
      return NextResponse.json(
        { error: 'Invalid income IDs provided' },
        { status: 400 }
      );
    }

    logger.info('Starting bulk delete of income transactions', {
      userId: user.id,
      incomeIds: validIds,
      count: validIds.length
    });

    // Get existing income transactions to check permissions and status
    const existingIncomes = await Income.find({
      _id: { $in: validIds }
    }).populate('createdBy', 'firstName lastName email');

    if (existingIncomes.length === 0) {
      return NextResponse.json(
        { error: 'No income transactions found with provided IDs' },
        { status: 404 }
      );
    }

    // Check permissions for each income
    const isAdmin = hasRequiredPermissions(user, [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]);
    const unauthorizedIncomes = existingIncomes.filter(income => {
      const isCreator = income.createdBy._id.toString() === user.id;
      return !isCreator && !isAdmin;
    });

    if (unauthorizedIncomes.length > 0) {
      return NextResponse.json(
        { 
          error: `You can only delete income transactions you created. ${unauthorizedIncomes.length} transactions are not authorized for deletion.`,
          unauthorizedIds: unauthorizedIncomes.map(i => i._id)
        },
        { status: 403 }
      );
    }

    // Check for income transactions that cannot be deleted (received status)
    const nonDeletableIncomes = existingIncomes.filter(income => 
      income.status === 'received' || income.status === 'approved'
    );

    if (nonDeletableIncomes.length > 0) {
      return NextResponse.json(
        { 
          error: `Cannot delete income transactions with 'received' or 'approved' status. ${nonDeletableIncomes.length} transactions cannot be deleted.`,
          nonDeletableIds: nonDeletableIncomes.map(i => i._id),
          nonDeletableReasons: nonDeletableIncomes.map(i => ({
            id: i._id,
            reference: i.reference,
            status: i.status,
            reason: `Status '${i.status}' prevents deletion`
          }))
        },
        { status: 400 }
      );
    }

    // Remove budget integrations for each income (run in background)
    const budgetCleanupPromises = existingIncomes
      .filter(income => income.appliedToBudget)
      .map(income => 
        budgetIncomeIntegrationService.removeIncomeAsBudgetItem(income)
          .catch(error => {
            logger.error('Error removing budget integration during bulk delete', {
              incomeId: income._id,
              error: error.message
            });
          })
      );

    // Run budget cleanup in background (don't wait for it)
    Promise.allSettled(budgetCleanupPromises);

    // Perform the bulk delete
    const deleteResult = await Income.deleteMany({
      _id: { $in: validIds }
    });

    logger.info('Bulk delete of income transactions completed', {
      userId: user.id,
      requestedCount: validIds.length,
      deletedCount: deleteResult.deletedCount,
      existingCount: existingIncomes.length
    });

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${deleteResult.deletedCount} income transactions`,
      deletedCount: deleteResult.deletedCount,
      requestedCount: validIds.length,
      details: {
        deletedIds: validIds,
        deletedTransactions: existingIncomes.map(income => ({
          id: income._id,
          reference: income.reference,
          amount: income.amount,
          source: income.source,
          status: income.status
        }))
      }
    });

  } catch (error: unknown) {
    logger.error('Error in bulk delete of income transactions', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
