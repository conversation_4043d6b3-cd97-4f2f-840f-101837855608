// app/(dashboard)/dashboard/auditors/recovery-center/page.tsx
import { Metada<PERSON> } from "next"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  RotateCcw, 
  Search, 
  Filter, 
  Clock,
  AlertTriangle,
  CheckCircle,
  Eye,
  FileText,
  Calendar,
  User,
  Shield,
  Trash2
} from "lucide-react"

export const metadata: Metadata = {
  title: "Recovery Center - Auditors",
  description: "Recover deleted items within the 90-day recovery window",
}

export default function RecoveryCenterPage() {
  // Mock data - replace with real data from API
  const recoverableItems = [
    {
      id: "del_001",
      originalId: "inc_12345",
      itemType: "Income Transaction",
      originalData: {
        reference: "INC-2025-001",
        amount: 150000,
        source: "Government Grant"
      },
      deletedBy: {
        name: "John Doe",
        email: "<EMAIL>",
        role: "Finance Manager"
      },
      deletionDate: "2025-01-15T10:30:00Z",
      deletionReason: "Duplicate entry created during data import process",
      recoveryDeadline: "2025-04-15T10:30:00Z",
      daysRemaining: 89,
      status: "Recoverable",
      priority: "High",
      complianceFlags: ["High Value", "Financial Record"]
    },
    {
      id: "del_002",
      originalId: "exp_67890",
      itemType: "Expenditure Record",
      originalData: {
        expenditureNumber: "EXP-2025-002",
        amount: 75000,
        title: "Office Supplies"
      },
      deletedBy: {
        name: "Jane Smith",
        email: "<EMAIL>",
        role: "Accountant"
      },
      deletionDate: "2025-01-14T14:20:00Z",
      deletionReason: "Data correction requested by supervisor",
      recoveryDeadline: "2025-04-14T14:20:00Z",
      daysRemaining: 88,
      status: "Recoverable",
      priority: "Medium",
      complianceFlags: ["Financial Record"]
    },
    {
      id: "del_003",
      originalId: "emp_11111",
      itemType: "Employee Record",
      originalData: {
        employeeNumber: "TCM-2024-001",
        firstName: "Michael",
        lastName: "Johnson"
      },
      deletedBy: {
        name: "Sarah Wilson",
        email: "<EMAIL>",
        role: "HR Manager"
      },
      deletionDate: "2024-11-15T09:15:00Z",
      deletionReason: "Employee left organization - contract terminated",
      recoveryDeadline: "2025-02-13T09:15:00Z",
      daysRemaining: 3,
      status: "Expiring Soon",
      priority: "Critical",
      complianceFlags: ["HR Record", "Personal Data", "Expiring"]
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Recoverable":
        return <Badge className="bg-green-100 text-green-700">Recoverable</Badge>
      case "Expiring Soon":
        return <Badge className="bg-red-100 text-red-700">Expiring Soon</Badge>
      case "Expired":
        return <Badge className="bg-gray-100 text-gray-700">Expired</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "Critical":
        return <Badge className="bg-red-100 text-red-700">Critical</Badge>
      case "High":
        return <Badge className="bg-orange-100 text-orange-700">High</Badge>
      case "Medium":
        return <Badge className="bg-yellow-100 text-yellow-700">Medium</Badge>
      case "Low":
        return <Badge className="bg-green-100 text-green-700">Low</Badge>
      default:
        return <Badge variant="secondary">{priority}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getDaysRemainingColor = (days: number) => {
    if (days <= 7) return "text-red-600"
    if (days <= 30) return "text-orange-600"
    return "text-green-600"
  }

  const criticalItems = recoverableItems.filter(item => item.daysRemaining <= 7).length
  const totalRecoverable = recoverableItems.filter(item => item.status === "Recoverable" || item.status === "Expiring Soon").length

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Recovery Center"
        text="Recover deleted items within the 90-day government compliance window"
      >
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Advanced Filters
          </Button>
          <Button size="sm">
            <RotateCcw className="h-4 w-4 mr-2" />
            Bulk Recovery
          </Button>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Recovery Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-50 rounded-full">
                  <RotateCcw className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Recoverable Items</p>
                  <p className="text-2xl font-bold">{totalRecoverable}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-red-50 rounded-full">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Expiring Soon</p>
                  <p className="text-2xl font-bold text-red-600">{criticalItems}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-50 rounded-full">
                  <Clock className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Recovery Window</p>
                  <p className="text-2xl font-bold">90 Days</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-50 rounded-full">
                  <CheckCircle className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Recovered Today</p>
                  <p className="text-2xl font-bold">3</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Critical Alerts */}
        {criticalItems > 0 && (
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertTitle className="text-red-800">Critical Recovery Alert</AlertTitle>
            <AlertDescription className="text-red-700">
              {criticalItems} item{criticalItems > 1 ? 's' : ''} will expire within 7 days. 
              Please review and recover immediately to prevent permanent loss.
            </AlertDescription>
          </Alert>
        )}

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Recovery Filters</CardTitle>
            <CardDescription>
              Filter recoverable items by type, priority, or expiration date
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search items..." className="pl-9" />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Item Type</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="income">Income Transactions</SelectItem>
                    <SelectItem value="expenditure">Expenditure Records</SelectItem>
                    <SelectItem value="employee">Employee Records</SelectItem>
                    <SelectItem value="budget">Budget Items</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Priority</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="All priorities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Priorities</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Expiration</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="All items" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Items</SelectItem>
                    <SelectItem value="expiring-7">Expiring in 7 days</SelectItem>
                    <SelectItem value="expiring-30">Expiring in 30 days</SelectItem>
                    <SelectItem value="safe">Safe (30+ days)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recoverable Items Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RotateCcw className="h-5 w-5 text-green-600" />
              Recoverable Items ({recoverableItems.length})
            </CardTitle>
            <CardDescription>
              Items available for recovery within the 90-day compliance window
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item Details</TableHead>
                    <TableHead>Deleted By</TableHead>
                    <TableHead>Deletion Date</TableHead>
                    <TableHead>Recovery Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recoverableItems.map((item) => (
                    <TableRow key={item.id} className={item.daysRemaining <= 7 ? "bg-red-50" : ""}>
                      <TableCell>
                        <div className="space-y-1">
                          <p className="font-medium text-sm">{item.itemType}</p>
                          <p className="text-xs text-muted-foreground">
                            ID: {item.originalId}
                          </p>
                          {item.originalData && (
                            <p className="text-xs text-gray-600">
                              {Object.entries(item.originalData).slice(0, 2).map(([key, value]) => 
                                `${key}: ${value}`
                              ).join(', ')}
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <p className="font-medium text-sm">{item.deletedBy.name}</p>
                          <p className="text-xs text-muted-foreground">{item.deletedBy.role}</p>
                          <p className="text-xs text-gray-600">{item.deletedBy.email}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <p className="text-sm">{formatDate(item.deletionDate)}</p>
                          <p className="text-xs text-muted-foreground">
                            Reason: {item.deletionReason.substring(0, 30)}...
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {getStatusBadge(item.status)}
                          <p className={`text-xs font-medium ${getDaysRemainingColor(item.daysRemaining)}`}>
                            {item.daysRemaining} days remaining
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Expires: {formatDate(item.recoveryDeadline)}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {getPriorityBadge(item.priority)}
                          <div className="flex flex-wrap gap-1">
                            {item.complianceFlags.slice(0, 2).map((flag, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {flag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          {item.status !== "Expired" && (
                            <Button 
                              size="sm" 
                              className={item.daysRemaining <= 7 ? "bg-red-600 hover:bg-red-700" : ""}
                            >
                              <RotateCcw className="h-4 w-4 mr-1" />
                              Recover
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Recovery Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-600" />
              Recovery Process Information
            </CardTitle>
            <CardDescription>
              Government compliance requirements for data recovery
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="font-medium text-sm mb-3">Recovery Window</h4>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-600" />
                    90-day recovery window from deletion date
                  </li>
                  <li className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-orange-600" />
                    Items expire automatically after deadline
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Recovery requires proper authorization
                  </li>
                  <li className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-purple-600" />
                    Complete recovery audit trail maintained
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-sm mb-3">Recovery Requirements</h4>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li className="flex items-center gap-2">
                    <User className="h-4 w-4 text-blue-600" />
                    AUDITOR or SUPER_ADMIN role required
                  </li>
                  <li className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-orange-600" />
                    Recovery reason must be documented
                  </li>
                  <li className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-green-600" />
                    Original data integrity verified
                  </li>
                  <li className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-purple-600" />
                    Recovery logged in audit trail
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}
