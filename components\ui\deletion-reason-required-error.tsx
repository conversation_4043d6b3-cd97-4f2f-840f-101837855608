'use client'

import React from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  AlertTriangle, 
  FileText, 
  RotateCcw, 
  Shield, 
  CheckCircle2,
  XCircle
} from "lucide-react"

interface DeletionReasonRequiredErrorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTryAgain: () => void;
  selectedCount: number;
  itemType: string;
  attemptedReason?: string;
  showComplianceInfo?: boolean;
}

export function DeletionReasonRequiredError({
  open,
  onOpenChange,
  onTryAgain,
  selectedCount,
  itemType,
  attemptedReason = '',
  showComplianceInfo = true
}: DeletionReasonRequiredErrorProps) {
  
  if (!open) return null;

  const handleTryAgain = () => {
    onTryAgain();
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  // Analyze the attempted reason to provide specific feedback
  const getReasonAnalysis = () => {
    const trimmedReason = attemptedReason.trim();
    const characterCount = trimmedReason.length;
    
    if (characterCount === 0) {
      return {
        issue: 'No deletion reason provided',
        suggestion: 'You must provide a detailed reason for audit compliance',
        severity: 'high'
      };
    } else if (characterCount < 10) {
      return {
        issue: `Reason too short (${characterCount}/10 characters)`,
        suggestion: 'Please provide more detail about why you are deleting these items',
        severity: 'medium'
      };
    } else {
      return {
        issue: 'Reason appears valid but validation failed',
        suggestion: 'Please try again or contact support if the issue persists',
        severity: 'low'
      };
    }
  };

  const reasonAnalysis = getReasonAnalysis();

  return (
    <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl mx-auto bg-white shadow-xl">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-red-100 rounded-full">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <div className="flex-1">
              <CardTitle className="text-red-800 text-lg">
                Deletion Reason Required
              </CardTitle>
              <CardDescription className="text-red-600">
                Government audit compliance requires a detailed deletion reason
              </CardDescription>
            </div>
            <Badge variant="destructive" className="text-xs">
              Validation Failed
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Error Summary */}
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertTitle>Validation Error</AlertTitle>
            <AlertDescription>
              <strong>{reasonAnalysis.issue}</strong>
              <br />
              {reasonAnalysis.suggestion}
            </AlertDescription>
          </Alert>

          {/* Selection Summary */}
          <div className="flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-3">
              <FileText className="h-5 w-5 text-red-600" />
              <div>
                <p className="font-medium text-red-800">
                  {selectedCount} {itemType}{selectedCount > 1 ? 's' : ''} selected for deletion
                </p>
                <p className="text-sm text-red-600">
                  Deletion cannot proceed without proper documentation
                </p>
              </div>
            </div>
          </div>

          {/* Attempted Reason Analysis */}
          {attemptedReason.trim().length > 0 && (
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Your Attempted Reason:</h4>
              <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
                <p className="text-sm text-gray-700 italic">
                  "{attemptedReason.trim()}"
                </p>
                <div className="mt-2 flex items-center gap-4 text-xs">
                  <span className={`${
                    attemptedReason.trim().length < 10 ? 'text-red-500' : 'text-green-600'
                  }`}>
                    {attemptedReason.trim().length}/10 minimum characters
                  </span>
                  <span className="text-gray-500">
                    {1000 - attemptedReason.trim().length} characters remaining
                  </span>
                </div>
              </div>
            </div>
          )}

          <Separator />

          {/* Requirements */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900 flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              Deletion Reason Requirements
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h5 className="text-sm font-medium text-gray-700">Minimum Requirements:</h5>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${
                      attemptedReason.trim().length >= 10 ? 'bg-green-500' : 'bg-red-500'
                    }`} />
                    At least 10 characters
                  </li>
                  <li className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${
                      attemptedReason.trim().length > 0 ? 'bg-green-500' : 'bg-red-500'
                    }`} />
                    Specific and detailed
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-blue-500" />
                    Business justification
                  </li>
                </ul>
              </div>
              <div className="space-y-2">
                <h5 className="text-sm font-medium text-gray-700">Good Examples:</h5>
                <ul className="text-xs text-gray-600 space-y-1">
                  <li>"• Duplicate entries from import error"</li>
                  <li>"• Supervisor requested data correction"</li>
                  <li>"• No longer relevant to fiscal year"</li>
                  <li>"• Created in wrong category by mistake"</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Compliance Information */}
          {showComplianceInfo && (
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertTitle>Government Audit Compliance</AlertTitle>
              <AlertDescription className="text-sm">
                The Teachers Council of Malawi follows government auditing standards that require 
                detailed documentation for all deletion operations. Your reason will be permanently 
                recorded and may be reviewed by auditors.
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4 border-t">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="flex items-center gap-2"
            >
              <XCircle className="h-4 w-4" />
              Cancel Deletion
            </Button>
            
            <Button
              onClick={handleTryAgain}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
            >
              <RotateCcw className="h-4 w-4" />
              Try Again
            </Button>
          </div>

          {/* Help Text */}
          <div className="text-center">
            <p className="text-xs text-gray-500">
              Need help? Contact your supervisor or system administrator for guidance on 
              providing appropriate deletion reasons.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default DeletionReasonRequiredError;
