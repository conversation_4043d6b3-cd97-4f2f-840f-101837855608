// app/(dashboard)/dashboard/auditors/audit-trail/page.tsx
import { Metadata } from "next"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { 
  FileText, 
  Search, 
  Filter, 
  Download, 
  Eye,
  Calendar,
  User,
  Shield,
  Clock,
  Trash2,
  Edit,
  Plus,
  AlertTriangle,
  CheckCircle,
  Globe
} from "lucide-react"

export const metadata: Metadata = {
  title: "Audit Trail - Auditors",
  description: "Complete audit trail of all system activities",
}

export default function AuditTrailPage() {
  // Mock data - replace with real data from API
  const auditEntries = [
    {
      id: "audit_001",
      timestamp: "2025-01-15T10:30:00Z",
      action: "DELETE",
      entityType: "Income Transaction",
      entityId: "inc_12345",
      userId: "user_001",
      userName: "John Doe",
      userRole: "Finance Manager",
      userEmail: "<EMAIL>",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      details: {
        reason: "Duplicate entry created during data import process",
        originalData: {
          reference: "INC-2025-001",
          amount: 150000,
          source: "Government Grant"
        },
        deletionType: "single"
      },
      severity: "MEDIUM",
      complianceFlags: ["High Value", "Financial Record"],
      sessionId: "sess_abc123"
    },
    {
      id: "audit_002",
      timestamp: "2025-01-15T09:45:00Z",
      action: "CREATE",
      entityType: "Employee Record",
      entityId: "emp_22222",
      userId: "user_002",
      userName: "Jane Smith",
      userRole: "HR Manager",
      userEmail: "<EMAIL>",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
      details: {
        newData: {
          employeeNumber: "TCM-2025-001",
          firstName: "Alice",
          lastName: "Johnson",
          department: "Finance"
        }
      },
      severity: "LOW",
      complianceFlags: ["HR Record", "Personal Data"],
      sessionId: "sess_def456"
    },
    {
      id: "audit_003",
      timestamp: "2025-01-15T08:20:00Z",
      action: "UPDATE",
      entityType: "Budget Item",
      entityId: "budget_333",
      userId: "user_003",
      userName: "Mike Wilson",
      userRole: "Budget Manager",
      userEmail: "<EMAIL>",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      details: {
        changes: {
          allocatedAmount: { from: 500000, to: 750000 },
          status: { from: "draft", to: "approved" }
        }
      },
      severity: "HIGH",
      complianceFlags: ["Budget Change", "High Value", "Approval Required"],
      sessionId: "sess_ghi789"
    },
    {
      id: "audit_004",
      timestamp: "2025-01-14T16:30:00Z",
      action: "LOGIN",
      entityType: "User Session",
      entityId: "session_444",
      userId: "user_004",
      userName: "Sarah Davis",
      userRole: "Auditor",
      userEmail: "<EMAIL>",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      details: {
        loginMethod: "password",
        mfaUsed: true
      },
      severity: "LOW",
      complianceFlags: ["Security Event"],
      sessionId: "sess_jkl012"
    }
  ]

  const getActionIcon = (action: string) => {
    switch (action) {
      case "DELETE":
        return <Trash2 className="h-4 w-4 text-red-600" />
      case "CREATE":
        return <Plus className="h-4 w-4 text-green-600" />
      case "UPDATE":
        return <Edit className="h-4 w-4 text-blue-600" />
      case "LOGIN":
        return <User className="h-4 w-4 text-purple-600" />
      default:
        return <FileText className="h-4 w-4 text-gray-600" />
    }
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "HIGH":
        return <Badge className="bg-red-100 text-red-700">High</Badge>
      case "MEDIUM":
        return <Badge className="bg-yellow-100 text-yellow-700">Medium</Badge>
      case "LOW":
        return <Badge className="bg-green-100 text-green-700">Low</Badge>
      default:
        return <Badge variant="secondary">{severity}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Audit Trail"
        text="Complete audit trail of all system activities for government compliance"
      >
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Trail
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Advanced Filters
          </Button>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Audit Trail Filters</CardTitle>
            <CardDescription>
              Filter audit entries by action, user, date, or entity type
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-5">
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search audit trail..." className="pl-9" />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Action</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="All actions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Actions</SelectItem>
                    <SelectItem value="create">Create</SelectItem>
                    <SelectItem value="update">Update</SelectItem>
                    <SelectItem value="delete">Delete</SelectItem>
                    <SelectItem value="login">Login</SelectItem>
                    <SelectItem value="logout">Logout</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Entity Type</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="All entities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Entities</SelectItem>
                    <SelectItem value="income">Income Transactions</SelectItem>
                    <SelectItem value="expenditure">Expenditure Records</SelectItem>
                    <SelectItem value="employee">Employee Records</SelectItem>
                    <SelectItem value="budget">Budget Items</SelectItem>
                    <SelectItem value="user">User Sessions</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Severity</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="All severities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Severities</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Date Range</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Last 24 hours" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">Last 24 hours</SelectItem>
                    <SelectItem value="7">Last 7 days</SelectItem>
                    <SelectItem value="30">Last 30 days</SelectItem>
                    <SelectItem value="90">Last 90 days</SelectItem>
                    <SelectItem value="all">All time</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Audit Trail Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              Audit Trail Entries ({auditEntries.length})
            </CardTitle>
            <CardDescription>
              Complete chronological record of all system activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>Action</TableHead>
                    <TableHead>Entity</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Details</TableHead>
                    <TableHead>Severity</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {auditEntries.map((entry) => (
                    <TableRow key={entry.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <p className="text-sm">{formatDate(entry.timestamp)}</p>
                          <p className="text-xs text-muted-foreground">
                            ID: {entry.id}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getActionIcon(entry.action)}
                          <span className="font-medium text-sm">{entry.action}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <p className="font-medium text-sm">{entry.entityType}</p>
                          <p className="text-xs text-muted-foreground">
                            ID: {entry.entityId}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <p className="font-medium text-sm">{entry.userName}</p>
                          <p className="text-xs text-muted-foreground">{entry.userRole}</p>
                          <div className="flex items-center gap-1 text-xs text-gray-600">
                            <Globe className="h-3 w-3" />
                            {entry.ipAddress}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 max-w-xs">
                          {entry.action === "DELETE" && entry.details.reason && (
                            <p className="text-xs text-gray-600">
                              Reason: {entry.details.reason.substring(0, 50)}...
                            </p>
                          )}
                          {entry.complianceFlags.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {entry.complianceFlags.slice(0, 2).map((flag, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {flag}
                                </Badge>
                              ))}
                              {entry.complianceFlags.length > 2 && (
                                <Badge variant="outline" className="text-xs">
                                  +{entry.complianceFlags.length - 2}
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getSeverityBadge(entry.severity)}
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Audit Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-50 rounded-full">
                  <FileText className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Total Entries</p>
                  <p className="text-2xl font-bold">12,847</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-red-50 rounded-full">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">High Severity</p>
                  <p className="text-2xl font-bold">23</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-50 rounded-full">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Compliant</p>
                  <p className="text-2xl font-bold">99.8%</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-50 rounded-full">
                  <Clock className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Last 24h</p>
                  <p className="text-2xl font-bold">156</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardShell>
  )
}
