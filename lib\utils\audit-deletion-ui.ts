// lib/utils/audit-deletion-ui.ts
import { toast } from '@/hooks/use-toast';

export interface DeletionReasonValidation {
  isValid: boolean;
  error?: string;
  characterCount: number;
  remainingCharacters: number;
}

export interface AuditDeletionUIConfig {
  minLength: number;
  maxLength: number;
  placeholder: string;
  title: string;
  description: string;
  warningMessage?: string;
}

export interface AuditDeletionState {
  deletionReason: string;
  isProcessing: boolean;
  showDialog: boolean;
  selectedItems: string[];
}

export class AuditDeletionUI {
  private static readonly DEFAULT_CONFIG: AuditDeletionUIConfig = {
    minLength: 10,
    maxLength: 1000,
    placeholder: "Please provide a detailed reason for deleting these items. This will be recorded in the audit trail for compliance purposes. Minimum 10 characters required.",
    title: "Confirm Deletion",
    description: "This action cannot be undone. The items will be moved to the audit trail for compliance.",
    warningMessage: "For government auditing compliance, all deletions require a detailed reason and are permanently recorded."
  };

  /**
   * Validate deletion reason
   */
  static validateDeletionReason(
    reason: string, 
    config: Partial<AuditDeletionUIConfig> = {}
  ): DeletionReasonValidation {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
    const trimmedReason = reason.trim();
    const characterCount = trimmedReason.length;
    const remainingCharacters = finalConfig.maxLength - characterCount;

    if (characterCount === 0) {
      return {
        isValid: false,
        error: 'Deletion reason is required for audit compliance',
        characterCount,
        remainingCharacters
      };
    }

    if (characterCount < finalConfig.minLength) {
      return {
        isValid: false,
        error: `Deletion reason must be at least ${finalConfig.minLength} characters for audit compliance`,
        characterCount,
        remainingCharacters
      };
    }

    if (characterCount > finalConfig.maxLength) {
      return {
        isValid: false,
        error: `Deletion reason cannot exceed ${finalConfig.maxLength} characters`,
        characterCount,
        remainingCharacters
      };
    }

    return {
      isValid: true,
      characterCount,
      remainingCharacters
    };
  }

  /**
   * Show validation error toast
   */
  static showValidationError(validation: DeletionReasonValidation): void {
    if (!validation.isValid && validation.error) {
      toast({
        title: "Validation Error",
        description: validation.error,
        variant: "destructive",
      });
    }
  }

  /**
   * Show success toast for audit deletion
   */
  static showSuccessToast(deletedCount: number, itemType: string = 'items'): void {
    toast({
      title: "Deletion Completed",
      description: `Successfully moved ${deletedCount} ${itemType} to audit trail for compliance`,
      variant: "default",
    });
  }

  /**
   * Show error toast for audit deletion
   */
  static showErrorToast(error: string): void {
    toast({
      title: "Deletion Failed",
      description: error,
      variant: "destructive",
    });
  }

  /**
   * Get character count display class
   */
  static getCharacterCountClass(validation: DeletionReasonValidation): string {
    if (validation.characterCount === 0) return 'text-muted-foreground';
    if (!validation.isValid) return 'text-red-500';
    return 'text-green-600';
  }

  /**
   * Get textarea border class based on validation
   */
  static getTextareaBorderClass(validation: DeletionReasonValidation): string {
    if (validation.characterCount === 0) return 'border-input';
    if (!validation.isValid) return 'border-red-500 focus:border-red-400';
    return 'border-green-200 focus:border-green-400';
  }

  /**
   * Generate deletion reason placeholder based on context
   */
  static getContextualPlaceholder(
    itemType: string, 
    action: string = 'deleting'
  ): string {
    return `Please provide a detailed reason for ${action} these ${itemType}. This will be recorded in the audit trail for government compliance purposes. Examples: "Duplicate entries created by error", "Data correction requested by supervisor", "Items no longer relevant to current fiscal year". Minimum 10 characters required.`;
  }

  /**
   * Generate dialog title based on context
   */
  static getContextualTitle(itemType: string, count: number): string {
    return `Delete ${count} ${itemType}${count > 1 ? 's' : ''}`;
  }

  /**
   * Generate dialog description based on context
   */
  static getContextualDescription(itemType: string, count: number): string {
    return `You are about to permanently delete ${count} ${itemType}${count > 1 ? 's' : ''}. This action cannot be undone, but the ${itemType}${count > 1 ? 's' : ''} will be moved to the audit trail for government compliance and can be reviewed by auditors.`;
  }

  /**
   * Perform audit deletion API call
   */
  static async performAuditDeletion(
    endpoint: string,
    ids: string[],
    deletionReason: string,
    additionalData: Record<string, any> = {}
  ): Promise<any> {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ids,
        deletionReason: deletionReason.trim(),
        ...additionalData
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to perform deletion');
    }

    return await response.json();
  }

  /**
   * Handle complete audit deletion workflow
   */
  static async handleAuditDeletion(
    endpoint: string,
    selectedItems: string[],
    deletionReason: string,
    itemType: string,
    config: Partial<AuditDeletionUIConfig> = {},
    additionalData: Record<string, any> = {}
  ): Promise<{ success: boolean; result?: any; error?: string }> {
    try {
      // Validate selection
      if (selectedItems.length === 0) {
        this.showErrorToast(`Please select ${itemType} to delete`);
        return { success: false, error: 'No items selected' };
      }

      // Validate deletion reason
      const validation = this.validateDeletionReason(deletionReason, config);
      if (!validation.isValid) {
        this.showValidationError(validation);
        return { success: false, error: validation.error };
      }

      // Perform deletion
      const result = await this.performAuditDeletion(
        endpoint,
        selectedItems,
        deletionReason,
        additionalData
      );

      // Show success message
      this.showSuccessToast(result.deletedCount || selectedItems.length, itemType);

      return { success: true, result };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete items';
      this.showErrorToast(errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Create deletion reason input props
   */
  static getDeletionReasonInputProps(
    deletionReason: string,
    onChange: (value: string) => void,
    config: Partial<AuditDeletionUIConfig> = {}
  ) {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
    const validation = this.validateDeletionReason(deletionReason, finalConfig);

    return {
      value: deletionReason,
      onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => onChange(e.target.value),
      placeholder: finalConfig.placeholder,
      maxLength: finalConfig.maxLength,
      className: `${this.getTextareaBorderClass(validation)} focus:ring-2 focus:ring-offset-2`,
      required: true,
      validation,
      characterCountClass: this.getCharacterCountClass(validation),
      showCharacterCount: true,
      showValidationMessage: true
    };
  }

  /**
   * Get default configuration for specific item types
   */
  static getConfigForItemType(itemType: 'income' | 'expenditure' | 'budget' | 'employee' | 'payroll'): AuditDeletionUIConfig {
    const baseConfig = { ...this.DEFAULT_CONFIG };

    switch (itemType) {
      case 'income':
        return {
          ...baseConfig,
          placeholder: this.getContextualPlaceholder('income transactions'),
          title: 'Delete Income Transactions',
          description: 'Income transactions will be moved to audit trail and budget integrations will be removed.',
          warningMessage: 'Deleting income transactions will affect budget calculations and financial reports.'
        };

      case 'expenditure':
        return {
          ...baseConfig,
          placeholder: this.getContextualPlaceholder('expenditure transactions'),
          title: 'Delete Expenditure Transactions',
          description: 'Expenditure transactions will be moved to audit trail for compliance review.',
          warningMessage: 'Deleting expenditure transactions will affect budget tracking and financial reports.'
        };

      case 'budget':
        return {
          ...baseConfig,
          placeholder: this.getContextualPlaceholder('budget items'),
          title: 'Delete Budget Items',
          description: 'Budget items will be moved to audit trail and may affect financial planning.',
          warningMessage: 'Deleting budget items will affect financial planning and reporting.'
        };

      case 'employee':
        return {
          ...baseConfig,
          placeholder: this.getContextualPlaceholder('employee records'),
          title: 'Delete Employee Records',
          description: 'Employee records will be moved to audit trail for HR compliance.',
          warningMessage: 'Deleting employee records will affect payroll and HR reporting.'
        };

      case 'payroll':
        return {
          ...baseConfig,
          placeholder: this.getContextualPlaceholder('payroll records'),
          title: 'Delete Payroll Records',
          description: 'Payroll records will be moved to audit trail for financial compliance.',
          warningMessage: 'Deleting payroll records will affect financial reporting and tax calculations.'
        };

      default:
        return baseConfig;
    }
  }
}

export default AuditDeletionUI;
