// lib/services/audit/audit-deletion-service.ts
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import DeletedItem from '@/models/audit/DeletedItems';
import { Model, Document } from 'mongoose';

export interface AuditDeletionContext {
  deletedBy: string;
  deletionReason: string;
  deletionType: 'single' | 'bulk';
  userInfo: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  context?: {
    fiscalYear?: string;
    department?: string;
    budgetId?: string;
    relatedTransactions?: string[];
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
  };
}

export interface DeletionValidationRule {
  field: string;
  allowedValues?: string[];
  disallowedValues?: string[];
  customValidator?: (value: any) => boolean;
  errorMessage: string;
}

export interface DeletionPermissionCheck {
  isAdmin: boolean;
  userId: string;
  checkOwnership?: boolean;
  ownershipField?: string; // Default: 'createdBy'
}

export interface AuditDeletionResult {
  success: boolean;
  deletedCount: number;
  auditRecordsCreated: number;
  deletedIds: string[];
  auditRecordIds: string[];
  errors?: Array<{
    id: string;
    error: string;
  }>;
  details: {
    deletionReason: string;
    auditCompliance: {
      auditTrailCreated: boolean;
      retentionPeriod: string;
      recoverable: boolean;
      complianceFlags: string[];
    };
  };
}

export class AuditDeletionService {
  /**
   * Validate deletion reason for audit compliance
   */
  static validateDeletionReason(reason: string): { valid: boolean; error?: string } {
    if (!reason || typeof reason !== 'string') {
      return { valid: false, error: 'Deletion reason is required' };
    }

    const trimmedReason = reason.trim();
    if (trimmedReason.length < 10) {
      return { 
        valid: false, 
        error: 'Deletion reason must be at least 10 characters long for audit compliance' 
      };
    }

    if (trimmedReason.length > 1000) {
      return { 
        valid: false, 
        error: 'Deletion reason cannot exceed 1000 characters' 
      };
    }

    return { valid: true };
  }

  /**
   * Validate items against deletion rules
   */
  static validateItemsForDeletion<T extends Document>(
    items: T[],
    validationRules: DeletionValidationRule[]
  ): { validItems: T[]; invalidItems: Array<{ item: T; errors: string[] }> } {
    const validItems: T[] = [];
    const invalidItems: Array<{ item: T; errors: string[] }> = [];

    for (const item of items) {
      const errors: string[] = [];

      for (const rule of validationRules) {
        const fieldValue = (item as any)[rule.field];

        // Check allowed values
        if (rule.allowedValues && !rule.allowedValues.includes(fieldValue)) {
          errors.push(rule.errorMessage);
          continue;
        }

        // Check disallowed values
        if (rule.disallowedValues && rule.disallowedValues.includes(fieldValue)) {
          errors.push(rule.errorMessage);
          continue;
        }

        // Custom validation
        if (rule.customValidator && !rule.customValidator(fieldValue)) {
          errors.push(rule.errorMessage);
          continue;
        }
      }

      if (errors.length === 0) {
        validItems.push(item);
      } else {
        invalidItems.push({ item, errors });
      }
    }

    return { validItems, invalidItems };
  }

  /**
   * Check permissions for deletion
   */
  static validatePermissions<T extends Document>(
    items: T[],
    permissionCheck: DeletionPermissionCheck
  ): { authorizedItems: T[]; unauthorizedItems: T[] } {
    if (permissionCheck.isAdmin) {
      return { authorizedItems: items, unauthorizedItems: [] };
    }

    if (!permissionCheck.checkOwnership) {
      return { authorizedItems: items, unauthorizedItems: [] };
    }

    const ownershipField = permissionCheck.ownershipField || 'createdBy';
    const authorizedItems: T[] = [];
    const unauthorizedItems: T[] = [];

    for (const item of items) {
      const createdBy = (item as any)[ownershipField];
      const createdById = createdBy && typeof createdBy === 'object' ? 
        (createdBy as any)?._id?.toString() : createdBy?.toString();
      
      if (createdById === permissionCheck.userId) {
        authorizedItems.push(item);
      } else {
        unauthorizedItems.push(item);
      }
    }

    return { authorizedItems, unauthorizedItems };
  }

  /**
   * Create audit trail records for deleted items
   */
  static async createAuditTrail<T extends Document>(
    items: T[],
    auditContext: AuditDeletionContext
  ): Promise<{ auditRecords: any[]; errors: Array<{ itemId: string; error: string }> }> {
    await connectToDatabase();

    const auditRecords: any[] = [];
    const errors: Array<{ itemId: string; error: string }> = [];

    for (const item of items) {
      try {
        const auditRecord = await DeletedItem.createDeletedRecord(item, auditContext);
        auditRecords.push(auditRecord);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push({
          itemId: item._id.toString(),
          error: errorMessage
        });
        
        logger.error('Error creating audit record', {
          itemId: item._id,
          modelName: item.constructor.modelName,
          error: errorMessage
        });
      }
    }

    return { auditRecords, errors };
  }

  /**
   * Perform audit-compliant bulk deletion
   */
  static async performAuditDeletion<T extends Document>(
    model: Model<T>,
    ids: string[],
    auditContext: AuditDeletionContext,
    options: {
      validationRules?: DeletionValidationRule[];
      permissionCheck?: DeletionPermissionCheck;
      populateFields?: string[];
      beforeDelete?: (items: T[]) => Promise<void>;
      afterDelete?: (deletedItems: T[], auditRecords: any[]) => Promise<void>;
    } = {}
  ): Promise<AuditDeletionResult> {
    try {
      await connectToDatabase();

      // Validate deletion reason
      const reasonValidation = this.validateDeletionReason(auditContext.deletionReason);
      if (!reasonValidation.valid) {
        throw new Error(reasonValidation.error);
      }

      // Validate IDs
      const validIds = ids.filter(id => 
        typeof id === 'string' && id.match(/^[0-9a-fA-F]{24}$/)
      );

      if (validIds.length !== ids.length) {
        throw new Error('Invalid IDs provided');
      }

      // Fetch existing items
      let query = model.find({ _id: { $in: validIds } });
      if (options.populateFields) {
        for (const field of options.populateFields) {
          query = query.populate(field);
        }
      }
      const existingItems = await query.exec();

      if (existingItems.length === 0) {
        throw new Error('No items found with provided IDs');
      }

      // Validate permissions
      let authorizedItems = existingItems;
      if (options.permissionCheck) {
        const permissionResult = this.validatePermissions(existingItems, options.permissionCheck);
        authorizedItems = permissionResult.authorizedItems;
        
        if (permissionResult.unauthorizedItems.length > 0) {
          throw new Error(
            `You can only delete items you created. ${permissionResult.unauthorizedItems.length} items are not authorized for deletion.`
          );
        }
      }

      // Validate deletion rules
      let validItems = authorizedItems;
      if (options.validationRules) {
        const validationResult = this.validateItemsForDeletion(authorizedItems, options.validationRules);
        validItems = validationResult.validItems;
        
        if (validationResult.invalidItems.length > 0) {
          const errorMessages = validationResult.invalidItems
            .map(invalid => `Item ${invalid.item._id}: ${invalid.errors.join(', ')}`)
            .join('; ');
          throw new Error(`Cannot delete items: ${errorMessages}`);
        }
      }

      // Run before delete hook
      if (options.beforeDelete) {
        await options.beforeDelete(validItems);
      }

      // Create audit trail
      const { auditRecords, errors: auditErrors } = await this.createAuditTrail(
        validItems, 
        auditContext
      );

      // Delete items from original collection
      const deletedIds = validItems.map(item => item._id);
      const deleteResult = await model.deleteMany({ _id: { $in: deletedIds } });

      // Run after delete hook
      if (options.afterDelete) {
        await options.afterDelete(validItems, auditRecords);
      }

      // Log completion
      logger.info('Audit deletion completed', {
        modelName: model.modelName,
        userId: auditContext.userInfo.id,
        requestedCount: ids.length,
        deletedCount: deleteResult.deletedCount,
        auditRecordsCreated: auditRecords.length,
        deletionReason: auditContext.deletionReason.trim()
      });

      return {
        success: true,
        deletedCount: deleteResult.deletedCount || 0,
        auditRecordsCreated: auditRecords.length,
        deletedIds: deletedIds.map(id => id.toString()),
        auditRecordIds: auditRecords.map(record => record._id.toString()),
        errors: auditErrors.length > 0 ? auditErrors : undefined,
        details: {
          deletionReason: auditContext.deletionReason.trim(),
          auditCompliance: {
            auditTrailCreated: true,
            retentionPeriod: '7 years',
            recoverable: true,
            complianceFlags: auditRecords.length > 0 ? auditRecords[0].complianceFlags || [] : []
          }
        }
      };

    } catch (error) {
      logger.error('Error in audit deletion', {
        modelName: model.modelName,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}

export default AuditDeletionService;
