// lib/services/audit/audit-deletion-service.ts
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import DeletedItem from '@/models/audit/DeletedItems';
import { Model, Document } from 'mongoose';
import { ErrorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';

export interface AuditDeletionContext {
  deletedBy: string;
  deletionReason: string;
  deletionType: 'single' | 'bulk';
  userInfo: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  context?: {
    fiscalYear?: string;
    department?: string;
    budgetId?: string;
    relatedTransactions?: string[];
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
  };
}

export interface DeletionValidationRule {
  field: string;
  allowedValues?: string[];
  disallowedValues?: string[];
  customValidator?: (value: any) => boolean;
  errorMessage: string;
}

export interface DeletionPermissionCheck {
  isAdmin: boolean;
  userId: string;
  checkOwnership?: boolean;
  ownershipField?: string; // Default: 'createdBy'
}

export interface AuditDeletionResult {
  success: boolean;
  deletedCount: number;
  auditRecordsCreated: number;
  deletedIds: string[];
  auditRecordIds: string[];
  errors?: Array<{
    itemId: string;
    error: string;
  }>;
  details: {
    deletionReason: string;
    auditCompliance: {
      auditTrailCreated: boolean;
      retentionPeriod: string;
      recoverable: boolean;
      complianceFlags: string[];
    };
  };
}

export class AuditDeletionService {
  private static errorService = ErrorService.getInstance();

  /**
   * Validate deletion reason for audit compliance
   */
  static validateDeletionReason(reason: string): { valid: boolean; error?: string } {
    if (!reason || typeof reason !== 'string') {
      return { valid: false, error: 'Deletion reason is required for audit compliance' };
    }

    const trimmedReason = reason.trim();
    if (trimmedReason.length < 10) {
      return {
        valid: false,
        error: 'Deletion reason must be at least 10 characters long for government audit compliance'
      };
    }

    if (trimmedReason.length > 1000) {
      return {
        valid: false,
        error: 'Deletion reason cannot exceed 1000 characters'
      };
    }

    return { valid: true };
  }

  /**
   * Validate items against deletion rules
   */
  static validateItemsForDeletion<T extends Document>(
    items: T[],
    validationRules: DeletionValidationRule[]
  ): { validItems: T[]; invalidItems: Array<{ item: T; errors: string[] }> } {
    const validItems: T[] = [];
    const invalidItems: Array<{ item: T; errors: string[] }> = [];

    for (const item of items) {
      const errors: string[] = [];

      for (const rule of validationRules) {
        const fieldValue = (item as any)[rule.field];

        // Check allowed values
        if (rule.allowedValues && !rule.allowedValues.includes(fieldValue)) {
          errors.push(rule.errorMessage);
          continue;
        }

        // Check disallowed values
        if (rule.disallowedValues && rule.disallowedValues.includes(fieldValue)) {
          errors.push(rule.errorMessage);
          continue;
        }

        // Custom validation
        if (rule.customValidator && !rule.customValidator(fieldValue)) {
          errors.push(rule.errorMessage);
          continue;
        }
      }

      if (errors.length === 0) {
        validItems.push(item);
      } else {
        invalidItems.push({ item, errors });
      }
    }

    return { validItems, invalidItems };
  }

  /**
   * Check permissions for deletion
   */
  static validatePermissions<T extends Document>(
    items: T[],
    permissionCheck: DeletionPermissionCheck
  ): { authorizedItems: T[]; unauthorizedItems: T[] } {
    if (permissionCheck.isAdmin) {
      return { authorizedItems: items, unauthorizedItems: [] };
    }

    if (!permissionCheck.checkOwnership) {
      return { authorizedItems: items, unauthorizedItems: [] };
    }

    const ownershipField = permissionCheck.ownershipField || 'createdBy';
    const authorizedItems: T[] = [];
    const unauthorizedItems: T[] = [];

    for (const item of items) {
      const createdBy = (item as any)[ownershipField];
      const createdById = createdBy && typeof createdBy === 'object' ? 
        (createdBy as any)?._id?.toString() : createdBy?.toString();
      
      if (createdById === permissionCheck.userId) {
        authorizedItems.push(item);
      } else {
        unauthorizedItems.push(item);
      }
    }

    return { authorizedItems, unauthorizedItems };
  }

  /**
   * Create audit trail records for deleted items
   */
  static async createAuditTrail<T extends Document>(
    items: T[],
    auditContext: AuditDeletionContext
  ): Promise<{ auditRecords: any[]; errors: Array<{ itemId: string; error: string }> }> {
    await connectToDatabase();

    const auditRecords: any[] = [];
    const errors: Array<{ itemId: string; error: string }> = [];

    for (const item of items) {
      try {
        const auditRecord = await (DeletedItem as any).createDeletedRecord(item, auditContext);
        auditRecords.push(auditRecord);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push({
          itemId: (item as any)._id?.toString() || 'unknown',
          error: errorMessage
        });

        logger.error('Error creating audit record', {
          itemId: (item as any)._id,
          modelName: (item.constructor as any).modelName,
          error: errorMessage
        });
      }
    }

    return { auditRecords, errors };
  }

  /**
   * Perform audit-compliant bulk deletion
   */
  static async performAuditDeletion<T extends Document>(
    model: Model<T>,
    ids: string[],
    auditContext: AuditDeletionContext,
    options: {
      validationRules?: DeletionValidationRule[];
      permissionCheck?: DeletionPermissionCheck;
      populateFields?: string[];
      beforeDelete?: (items: T[]) => Promise<void>;
      afterDelete?: (deletedItems: T[], auditRecords: any[]) => Promise<void>;
    } = {}
  ): Promise<AuditDeletionResult> {
    try {
      await connectToDatabase();

      // Validate deletion reason
      const reasonValidation = this.validateDeletionReason(auditContext.deletionReason);
      if (!reasonValidation.valid) {
        const error = this.errorService.createError(
          ErrorType.VALIDATION,
          'AUDIT_DELETION_INVALID_REASON',
          reasonValidation.error || 'Invalid deletion reason',
          reasonValidation.error || 'Please provide a valid deletion reason for audit compliance',
          {
            userId: auditContext.userInfo.id,
            endpoint: 'audit-deletion',
            method: 'DELETE'
          },
          ErrorSeverity.MEDIUM,
          'Deletion reason validation failed for audit compliance requirements'
        );
        throw new Error(error.userMessage);
      }

      // Validate IDs
      const validIds = ids.filter(id =>
        typeof id === 'string' && id.match(/^[0-9a-fA-F]{24}$/)
      );

      if (validIds.length !== ids.length) {
        const error = this.errorService.createError(
          ErrorType.VALIDATION,
          'AUDIT_DELETION_INVALID_IDS',
          'One or more invalid MongoDB ObjectIDs provided',
          'Some of the selected items have invalid identifiers and cannot be deleted',
          {
            userId: auditContext.userInfo.id,
            endpoint: 'audit-deletion',
            method: 'DELETE',
            additionalData: {
              totalIds: ids.length,
              validIds: validIds.length,
              invalidIds: ids.filter(id => !validIds.includes(id))
            }
          },
          ErrorSeverity.MEDIUM,
          'ID validation failed - all IDs must be valid 24-character MongoDB ObjectIDs'
        );
        throw new Error(error.userMessage);
      }

      // Fetch existing items
      let query = model.find({ _id: { $in: validIds } });
      if (options.populateFields) {
        for (const field of options.populateFields) {
          query = query.populate(field);
        }
      }
      const existingItems = await query.exec();

      if (existingItems.length === 0) {
        const error = this.errorService.createError(
          ErrorType.NOT_FOUND,
          'AUDIT_DELETION_NO_ITEMS',
          'No items found with provided IDs',
          'The selected items could not be found. They may have already been deleted or the IDs are incorrect.',
          {
            userId: auditContext.userInfo.id,
            endpoint: 'audit-deletion',
            method: 'DELETE',
            additionalData: {
              requestedIds: validIds,
              modelName: model.modelName
            }
          },
          ErrorSeverity.MEDIUM,
          'Database query returned no matching records for the provided IDs'
        );
        throw new Error(error.userMessage);
      }

      // Validate permissions
      let authorizedItems = existingItems;
      if (options.permissionCheck) {
        const permissionResult = this.validatePermissions(existingItems, options.permissionCheck);
        authorizedItems = permissionResult.authorizedItems;

        if (permissionResult.unauthorizedItems.length > 0) {
          const error = this.errorService.createError(
            ErrorType.FORBIDDEN,
            'AUDIT_DELETION_UNAUTHORIZED',
            'Insufficient permissions for deletion',
            `You can only delete items you created. ${permissionResult.unauthorizedItems.length} items are not authorized for deletion.`,
            {
              userId: auditContext.userInfo.id,
              userRole: auditContext.userInfo.role,
              endpoint: 'audit-deletion',
              method: 'DELETE',
              additionalData: {
                totalItems: existingItems.length,
                authorizedItems: authorizedItems.length,
                unauthorizedItems: permissionResult.unauthorizedItems.length,
                modelName: model.modelName
              }
            },
            ErrorSeverity.HIGH,
            'User attempted to delete items they do not own without admin privileges',
            [
              'You can only delete items you created',
              'Contact an administrator if you need to delete other users\' items',
              'Verify you are logged in with the correct account'
            ]
          );
          throw new Error(error.userMessage);
        }
      }

      // Validate deletion rules
      let validItems = authorizedItems;
      if (options.validationRules) {
        const validationResult = this.validateItemsForDeletion(authorizedItems, options.validationRules);
        validItems = validationResult.validItems;

        if (validationResult.invalidItems.length > 0) {
          const errorMessages = validationResult.invalidItems
            .map(invalid => `Item ${invalid.item._id}: ${invalid.errors.join(', ')}`)
            .join('; ');

          const error = this.errorService.createError(
            ErrorType.BUSINESS_LOGIC,
            'AUDIT_DELETION_VALIDATION_FAILED',
            'Items failed deletion validation rules',
            `Cannot delete items due to business rule violations: ${errorMessages}`,
            {
              userId: auditContext.userInfo.id,
              endpoint: 'audit-deletion',
              method: 'DELETE',
              additionalData: {
                totalItems: authorizedItems.length,
                validItems: validItems.length,
                invalidItems: validationResult.invalidItems.length,
                validationErrors: validationResult.invalidItems.map(invalid => ({
                  itemId: invalid.item._id,
                  errors: invalid.errors
                })),
                modelName: model.modelName
              }
            },
            ErrorSeverity.MEDIUM,
            'Business rules prevent deletion of some items (e.g., status restrictions)',
            [
              'Check the status of items you are trying to delete',
              'Some items may be in a state that prevents deletion',
              'Contact an administrator if you believe this is an error'
            ]
          );
          throw new Error(error.userMessage);
        }
      }

      // Run before delete hook
      if (options.beforeDelete) {
        await options.beforeDelete(validItems);
      }

      // Create audit trail
      const { auditRecords, errors: auditErrors } = await this.createAuditTrail(
        validItems, 
        auditContext
      );

      // Delete items from original collection
      const deletedIds = validItems.map(item => item._id);
      const deleteResult = await model.deleteMany({ _id: { $in: deletedIds } });

      // Run after delete hook
      if (options.afterDelete) {
        await options.afterDelete(validItems, auditRecords);
      }

      // Log completion
      logger.info('Audit deletion completed', {
        modelName: model.modelName,
        userId: auditContext.userInfo.id,
        requestedCount: ids.length,
        deletedCount: deleteResult.deletedCount,
        auditRecordsCreated: auditRecords.length,
        deletionReason: auditContext.deletionReason.trim()
      });

      return {
        success: true,
        deletedCount: deleteResult.deletedCount || 0,
        auditRecordsCreated: auditRecords.length,
        deletedIds: deletedIds.map(id => (id as any).toString()),
        auditRecordIds: auditRecords.map(record => (record as any)._id.toString()),
        errors: auditErrors.length > 0 ? auditErrors : undefined,
        details: {
          deletionReason: auditContext.deletionReason.trim(),
          auditCompliance: {
            auditTrailCreated: true,
            retentionPeriod: '7 years',
            recoverable: true,
            complianceFlags: auditRecords.length > 0 ? auditRecords[0].complianceFlags || [] : []
          }
        }
      };

    } catch (error) {
      // Log the error using the error service
      const auditError = this.errorService.createError(
        ErrorType.SYSTEM,
        'AUDIT_DELETION_SYSTEM_ERROR',
        'System error during audit deletion',
        error instanceof Error ? error.message : 'An unexpected error occurred during the deletion process',
        {
          userId: auditContext.userInfo.id,
          endpoint: 'audit-deletion',
          method: 'DELETE',
          additionalData: {
            modelName: model.modelName,
            requestedIds: ids.length,
            originalError: error instanceof Error ? error.message : 'Unknown error'
          }
        },
        ErrorSeverity.HIGH,
        'Audit deletion process failed due to system error',
        [
          'Please try again in a few moments',
          'If the problem persists, contact system administrator',
          'Check your network connection and try again'
        ]
      );

      logger.error('Error in audit deletion', {
        modelName: model.modelName,
        userId: auditContext.userInfo.id,
        errorCode: auditError.code,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      throw error;
    }
  }
}

export default AuditDeletionService;
