# Audit Deletion Service Documentation

## Table of Contents
1. [Overview](#overview)
2. [User Guide](#user-guide)
3. [Developer Guide](#developer-guide)
4. [Management Guide](#management-guide)
5. [API Reference](#api-reference)
6. [Compliance & Security](#compliance--security)
7. [Troubleshooting](#troubleshooting)

---

## Overview

The Audit Deletion Service is a comprehensive, government-compliant deletion system designed for the Teachers Council of Malawi (TCM) HR Management System. It ensures full transparency, accountability, and compliance with government auditing standards by moving deleted items to a secure audit trail instead of permanent deletion.

### Key Features
- ✅ **Government Compliance** - 7-year retention period with full audit trail
- ✅ **Mandatory Deletion Reasons** - 10+ character requirement for all deletions
- ✅ **Role-Based Access** - Secure permissions with ownership validation
- ✅ **Complete Audit Trail** - User info, timestamps, IP addresses, reasons
- ✅ **Recovery Support** - 90-day recovery window for deleted items
- ✅ **Modular Design** - Reusable across all system modules

### Supported Modules
- Income Transactions
- Expenditure Transactions
- Budget Items
- Employee Records
- Payroll Records
- Any future modules

---

## User Guide

### For End Users

#### How to Delete Items

1. **Select Items**
   - Use checkboxes to select items you want to delete
   - Only items you created can be deleted (unless you're an admin)

2. **Click Delete Button**
   - Look for the red "Delete Selected" button
   - Button appears when items are selected

3. **Provide Deletion Reason**
   - **REQUIRED**: Enter at least 10 characters explaining why you're deleting
   - Be specific and detailed for audit compliance
   - Examples:
     - "Duplicate entries created by error during data import"
     - "Data correction requested by supervisor on 2025-01-15"
     - "Items no longer relevant to current fiscal year 2025-2026"

4. **Confirm Deletion**
   - Review the warning about permanent deletion
   - Click "Delete X Items" to confirm
   - Items are moved to audit trail (not permanently deleted)

#### What You Can Delete

✅ **Allowed Statuses:**
- Draft items
- Pending approval items
- Rejected items
- Cancelled items

❌ **Protected Statuses:**
- Approved transactions
- Received income
- Paid expenditures
- Finalized records

#### Deletion Rules

- **Ownership**: You can only delete items you created
- **Admin Override**: Super admins and system admins can delete any items
- **Status Restrictions**: Some statuses prevent deletion for data integrity
- **Audit Trail**: All deletions are permanently recorded

### For Auditors

#### Accessing Deleted Items

1. **Login Required**: AUDITOR or SUPER_ADMIN role only
2. **Navigate to Audit Module**: Available in admin sidebar
3. **View Deleted Items**: Complete audit trail with:
   - Original item data
   - Deletion reason
   - User who deleted
   - Timestamp and IP address
   - Recovery status

#### Audit Reports

- **Excel Export**: Complete audit trail data
- **PDF Reports**: Formatted audit summaries
- **Filtering**: By date, user, module, reason
- **Search**: Full-text search across deletion reasons

---

## Developer Guide

### Quick Start

#### 1. Basic Implementation

```typescript
import { AuditDeletionService } from '@/lib/services/audit/audit-deletion-service';

// Define validation rules
const validationRules = [
  {
    field: 'status',
    disallowedValues: ['approved', 'paid'],
    errorMessage: 'Cannot delete approved or paid items'
  }
];

// Define permissions
const permissionCheck = {
  isAdmin: hasRequiredPermissions(user, [UserRole.SUPER_ADMIN]),
  userId: user.id,
  checkOwnership: true,
  ownershipField: 'createdBy'
};

// Perform deletion
const result = await AuditDeletionService.performAuditDeletion(
  YourModel,
  ids,
  auditContext,
  { validationRules, permissionCheck }
);
```

#### 2. Frontend Integration

```typescript
import { AuditDeletionUI } from '@/lib/utils/audit-deletion-ui';
import { AuditDeletionDialog } from '@/components/ui/audit-deletion-dialog';

// Handle deletion
const handleDeletion = async (deletionReason: string) => {
  const result = await AuditDeletionUI.handleAuditDeletion(
    '/api/your-endpoint',
    selectedIds,
    deletionReason,
    'your item type'
  );
};

// UI Component
<AuditDeletionDialog
  open={showDialog}
  onOpenChange={setShowDialog}
  onConfirm={handleDeletion}
  selectedCount={items.length}
  itemType="your item type"
/>
```

### API Route Implementation

#### Option 1: Using Existing Service

```typescript
// app/api/your-module/bulk-delete/route.ts
import { AuditDeletionService } from '@/lib/services/audit/audit-deletion-service';

export async function POST(req: NextRequest) {
  // ... authentication and validation ...
  
  const result = await AuditDeletionService.performAuditDeletion(
    YourModel,
    ids,
    auditContext,
    options
  );
  
  return NextResponse.json(result);
}
```

#### Option 2: Using Universal Endpoints

```typescript
// Frontend call to universal endpoint
const response = await fetch('/api/audit/delete/bulk', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    modelName: 'YourModel',
    ids: selectedIds,
    deletionReason: reason
  })
});
```

### Configuration Options

#### Validation Rules

```typescript
const validationRules = [
  {
    field: 'status',
    allowedValues: ['draft', 'pending'],
    errorMessage: 'Only draft and pending items can be deleted'
  },
  {
    field: 'amount',
    customValidator: (amount) => amount < 1000000,
    errorMessage: 'High-value transactions require special approval'
  }
];
```

#### Permission Configuration

```typescript
const permissionCheck = {
  isAdmin: hasRequiredPermissions(user, [UserRole.SUPER_ADMIN]),
  userId: user.id,
  checkOwnership: true,
  ownershipField: 'createdBy' // or 'userId', 'owner', etc.
};
```

#### Hooks and Callbacks

```typescript
const options = {
  beforeDelete: async (items) => {
    // Cleanup related data
    await cleanupRelatedRecords(items);
  },
  afterDelete: async (deletedItems, auditRecords) => {
    // Send notifications
    await notifyStakeholders(deletedItems);
  }
};
```

### Error Handling

The service integrates with the existing error service for consistent error handling:

```typescript
// Automatic error service integration
try {
  const result = await AuditDeletionService.performAuditDeletion(...);
} catch (error) {
  // Error is automatically logged with context
  // User-friendly message is provided
  // Audit trail is maintained
}
```

---

## Management Guide

### For System Administrators

#### Monitoring Deletions

1. **Dashboard Metrics**
   - Daily deletion counts
   - Top deletion reasons
   - User activity patterns
   - Error rates

2. **Audit Reports**
   - Weekly deletion summaries
   - Compliance reports
   - User access logs
   - Recovery statistics

#### Configuration Management

1. **Retention Policies**
   ```typescript
   // Default: 7 years (government standard)
   retentionPeriod: 2555 // days
   ```

2. **Recovery Windows**
   ```typescript
   // Default: 90 days
   recoveryDeadline: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
   ```

3. **Validation Rules**
   - Configure per module
   - Status-based restrictions
   - Amount thresholds
   - Custom business rules

#### User Management

1. **Role Permissions**
   - `SUPER_ADMIN`: Can delete any items, access audit trail
   - `SYSTEM_ADMIN`: Can delete any items, access audit trail
   - `AUDITOR`: Read-only access to audit trail
   - `FINANCE_MANAGER`: Can delete financial records they created
   - `ACCOUNTANT`: Can delete accounting records they created

2. **Access Control**
   - Ownership validation
   - Role-based restrictions
   - IP address logging
   - Session tracking

### For Compliance Officers

#### Audit Trail Features

1. **Complete Record Keeping**
   - Original item data preserved
   - User identification
   - Deletion timestamp
   - IP address and user agent
   - Detailed deletion reason

2. **Compliance Flags**
   - High-value transactions
   - Financial audit requirements
   - Regulatory compliance markers
   - Review status tracking

3. **Retention Management**
   - 7-year government standard
   - Automatic retention tracking
   - Recovery deadline monitoring
   - Compliance reporting

#### Reporting Capabilities

1. **Standard Reports**
   - Daily deletion summary
   - Weekly compliance report
   - Monthly audit trail export
   - Annual retention review

2. **Custom Reports**
   - Filter by date range
   - Filter by user/role
   - Filter by module/type
   - Filter by deletion reason

3. **Export Formats**
   - Excel spreadsheets
   - PDF reports
   - CSV data exports
   - JSON audit logs

### For Department Heads

#### Oversight Functions

1. **Team Activity Monitoring**
   - Staff deletion patterns
   - Reason quality assessment
   - Compliance adherence
   - Training needs identification

2. **Approval Workflows**
   - High-value deletion approvals
   - Bulk operation oversight
   - Exception handling
   - Policy enforcement

3. **Performance Metrics**
   - Deletion accuracy rates
   - Reason quality scores
   - Compliance percentages
   - Error reduction trends

---

## API Reference

### Core Service Methods

#### `AuditDeletionService.performAuditDeletion()`

```typescript
static async performAuditDeletion<T extends Document>(
  model: Model<T>,
  ids: string[],
  auditContext: AuditDeletionContext,
  options?: AuditDeletionOptions
): Promise<AuditDeletionResult>
```

**Parameters:**
- `model`: Mongoose model to delete from
- `ids`: Array of item IDs to delete
- `auditContext`: User and deletion context
- `options`: Validation rules, permissions, hooks

**Returns:**
- `AuditDeletionResult`: Success status, counts, audit records

#### `AuditDeletionService.validateDeletionReason()`

```typescript
static validateDeletionReason(reason: string): {
  valid: boolean;
  error?: string;
}
```

### UI Utility Methods

#### `AuditDeletionUI.handleAuditDeletion()`

```typescript
static async handleAuditDeletion(
  endpoint: string,
  selectedItems: string[],
  deletionReason: string,
  itemType: string,
  config?: Partial<AuditDeletionUIConfig>
): Promise<{ success: boolean; result?: any; error?: string }>
```

### API Endpoints

#### Universal Endpoints

- `POST /api/audit/delete/single` - Single item deletion
- `POST /api/audit/delete/bulk` - Bulk item deletion
- `GET /api/audit/deleted-items` - View audit trail (AUDITOR+ only)
- `GET /api/audit/reports` - Generate reports (AUDITOR+ only)

#### Module-Specific Endpoints

- `POST /api/accounting/income/bulk-delete`
- `POST /api/accounting/expenditure/bulk-delete`
- `POST /api/hr/employee/bulk-delete`
- `POST /api/payroll/bulk-delete`

---

## Compliance & Security

### Government Compliance

1. **Audit Trail Requirements**
   - Complete data preservation
   - User accountability
   - Timestamp accuracy
   - Reason documentation

2. **Retention Standards**
   - 7-year minimum retention
   - Secure storage
   - Access logging
   - Recovery procedures

3. **Access Control**
   - Role-based permissions
   - Ownership validation
   - Administrative oversight
   - Audit access restrictions

### Security Features

1. **Data Protection**
   - Encrypted audit storage
   - Secure transmission
   - Access logging
   - IP address tracking

2. **Permission System**
   - Multi-level authorization
   - Ownership verification
   - Admin overrides
   - Audit trail access

3. **Monitoring**
   - Real-time logging
   - Error tracking
   - Performance monitoring
   - Security alerts

---

## Troubleshooting

### Common Issues

#### "Deletion reason too short"
- **Cause**: Reason less than 10 characters
- **Solution**: Provide detailed explanation (minimum 10 characters)
- **Example**: "Duplicate entry created by error"

#### "Insufficient permissions"
- **Cause**: Trying to delete items you didn't create
- **Solution**: Contact admin or delete only your own items
- **Admin**: Can override with proper authorization

#### "Cannot delete approved items"
- **Cause**: Business rules prevent deletion of certain statuses
- **Solution**: Change status first or contact supervisor
- **Override**: Admin approval may be required

#### "Items not found"
- **Cause**: Items already deleted or IDs incorrect
- **Solution**: Refresh page and verify selection
- **Check**: Audit trail for previous deletions

### Error Codes

- `AUDIT_DELETION_INVALID_REASON`: Deletion reason validation failed
- `AUDIT_DELETION_INVALID_IDS`: Invalid MongoDB ObjectIDs
- `AUDIT_DELETION_NO_ITEMS`: No items found with provided IDs
- `AUDIT_DELETION_UNAUTHORIZED`: Insufficient permissions
- `AUDIT_DELETION_VALIDATION_FAILED`: Business rule violations
- `AUDIT_DELETION_SYSTEM_ERROR`: Technical/database errors

### Support Contacts

- **Technical Issues**: System Administrator
- **Permission Issues**: Department Head
- **Compliance Questions**: Audit Officer
- **Training Needs**: HR Department

---

## Version History

- **v1.0.0** - Initial implementation with basic audit trail
- **v1.1.0** - Added error service integration
- **v1.2.0** - Enhanced UI components and validation
- **v1.3.0** - Universal API endpoints and modular design

---

*This documentation is maintained by the TCM Development Team. Last updated: January 2025*
