'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AuditDeletionDialog } from "@/components/ui/audit-deletion-dialog"
import { Trash2, FileText } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

// Example data structure
interface ExampleItem {
  id: string;
  name: string;
  status: 'draft' | 'pending' | 'approved';
  createdBy: string;
}

const exampleItems: ExampleItem[] = [
  { id: '1', name: 'Income Transaction #001', status: 'draft', createdBy: 'user1' },
  { id: '2', name: 'Income Transaction #002', status: 'pending', createdBy: 'user1' },
  { id: '3', name: 'Income Transaction #003', status: 'approved', createdBy: 'user2' },
  { id: '4', name: 'Income Transaction #004', status: 'draft', createdBy: 'user1' },
];

export function AuditDeletionExample() {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  // Handle item selection
  const handleItemSelect = (itemId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, itemId]);
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId));
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(exampleItems.map(item => item.id));
    } else {
      setSelectedItems([]);
    }
  };

  // Handle deletion confirmation
  const handleDeleteConfirm = async (deletionReason: string) => {
    setIsProcessing(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate success
      toast({
        title: "Deletion Completed",
        description: `Successfully moved ${selectedItems.length} items to audit trail`,
        variant: "default",
      });
      
      // Reset state
      setSelectedItems([]);
      setShowDeleteDialog(false);
      
    } catch (error) {
      toast({
        title: "Deletion Failed",
        description: "An error occurred during deletion",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const selectedCount = selectedItems.length;
  const allSelected = selectedItems.length === exampleItems.length;
  const someSelected = selectedItems.length > 0 && selectedItems.length < exampleItems.length;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Audit Deletion Example
          </CardTitle>
          <CardDescription>
            This example demonstrates the audit deletion system with the new error handling component.
            Try deleting items without providing a proper deletion reason to see the error component.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Selection Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Checkbox
                checked={allSelected}
                ref={(el) => {
                  if (el) el.indeterminate = someSelected;
                }}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm font-medium">
                Select All ({selectedCount} selected)
              </span>
            </div>
            
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setShowDeleteDialog(true)}
              disabled={selectedCount === 0}
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Delete Selected ({selectedCount})
            </Button>
          </div>

          {/* Items List */}
          <div className="space-y-2">
            {exampleItems.map((item) => (
              <div
                key={item.id}
                className={`flex items-center justify-between p-3 border rounded-lg ${
                  selectedItems.includes(item.id) ? 'bg-blue-50 border-blue-200' : 'bg-white'
                }`}
              >
                <div className="flex items-center gap-3">
                  <Checkbox
                    checked={selectedItems.includes(item.id)}
                    onCheckedChange={(checked) => handleItemSelect(item.id, checked as boolean)}
                  />
                  <div>
                    <p className="font-medium">{item.name}</p>
                    <p className="text-sm text-gray-500">Created by: {item.createdBy}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    item.status === 'draft' ? 'bg-gray-100 text-gray-700' :
                    item.status === 'pending' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-green-100 text-green-700'
                  }`}>
                    {item.status}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* Instructions */}
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Try These Scenarios:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• <strong>Empty reason:</strong> Click delete without entering any reason</li>
              <li>• <strong>Short reason:</strong> Enter less than 10 characters (e.g., "test")</li>
              <li>• <strong>Valid reason:</strong> Enter 10+ characters (e.g., "Duplicate entries from import error")</li>
            </ul>
            <p className="text-xs text-blue-600 mt-2">
              The system will show different UI components based on the validation error type.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Audit Deletion Dialog with Error Handling */}
      <AuditDeletionDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleDeleteConfirm}
        selectedCount={selectedCount}
        itemType="income transaction"
        isProcessing={isProcessing}
        showComplianceInfo={true}
      />
    </div>
  );
}

export default AuditDeletionExample;
