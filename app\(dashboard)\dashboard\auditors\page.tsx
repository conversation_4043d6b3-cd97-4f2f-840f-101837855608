// app/(dashboard)/dashboard/auditors/page.tsx
import { <PERSON>ada<PERSON> } from "next"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { 
  FileSearch, 
  Trash2, 
  FileText, 
  BarChart3, 
  RotateCcw, 
  Settings,
  Shield,
  Clock,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Database,
  Users,
  Calendar
} from "lucide-react"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Auditors Dashboard",
  description: "Government audit compliance and deleted items management",
}

export default function AuditorsPage() {
  // Mock data - replace with real data from API
  const auditStats = {
    totalDeletedItems: 1247,
    pendingRecovery: 23,
    complianceScore: 98.5,
    lastAuditDate: "2025-01-10",
    retentionItems: 892,
    criticalAlerts: 2
  }

  const recentDeletions = [
    {
      id: "1",
      itemType: "Income Transaction",
      deletedBy: "<PERSON>",
      deletionDate: "2025-01-15",
      reason: "Duplicate entry from import error",
      status: "Compliant"
    },
    {
      id: "2", 
      itemType: "Expenditure Record",
      deletedBy: "Jane Smith",
      deletionDate: "2025-01-14",
      reason: "Data correction requested by supervisor",
      status: "Under Review"
    },
    {
      id: "3",
      itemType: "Employee Record",
      deletedBy: "Mike Johnson",
      deletionDate: "2025-01-13",
      reason: "Employee left organization",
      status: "Compliant"
    }
  ]

  const quickActions = [
    {
      title: "View Deleted Items",
      description: "Browse all deleted items with full audit trail",
      href: "/dashboard/auditors/deleted-items",
      icon: Trash2,
      color: "bg-red-50 text-red-600 border-red-200"
    },
    {
      title: "Audit Trail",
      description: "Complete audit trail of all system activities",
      href: "/dashboard/auditors/audit-trail",
      icon: FileText,
      color: "bg-blue-50 text-blue-600 border-blue-200"
    },
    {
      title: "Compliance Reports",
      description: "Generate compliance reports for government auditing",
      href: "/dashboard/auditors/compliance-reports",
      icon: BarChart3,
      color: "bg-green-50 text-green-600 border-green-200"
    },
    {
      title: "Recovery Center",
      description: "Recover deleted items within 90-day window",
      href: "/dashboard/auditors/recovery-center",
      icon: RotateCcw,
      color: "bg-orange-50 text-orange-600 border-orange-200"
    }
  ]

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Auditors Dashboard"
        text="Government audit compliance and deleted items management for Teachers Council of Malawi"
      >
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-green-600 border-green-200">
            <Shield className="h-3 w-3 mr-1" />
            Government Compliant
          </Badge>
          <Link href="/dashboard/auditors/settings">
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </Link>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Deleted Items</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{auditStats.totalDeletedItems.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                7-year retention period
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Recovery</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{auditStats.pendingRecovery}</div>
              <p className="text-xs text-muted-foreground">
                Within 90-day window
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Compliance Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{auditStats.complianceScore}%</div>
              <p className="text-xs text-muted-foreground">
                Government standards
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Critical Alerts</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{auditStats.criticalAlerts}</div>
              <p className="text-xs text-muted-foreground">
                Require attention
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Access key audit and compliance functions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {quickActions.map((action) => (
                <Link key={action.href} href={action.href}>
                  <Card className={`cursor-pointer transition-colors hover:bg-accent ${action.color} border-2`}>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <action.icon className="h-6 w-6" />
                        <div>
                          <h3 className="font-medium text-sm">{action.title}</h3>
                          <p className="text-xs opacity-80">{action.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Deletions */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Deletions</CardTitle>
            <CardDescription>
              Latest items moved to audit trail
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentDeletions.map((deletion, index) => (
                <div key={deletion.id}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-red-50 rounded-full">
                        <Trash2 className="h-4 w-4 text-red-600" />
                      </div>
                      <div>
                        <p className="font-medium text-sm">{deletion.itemType}</p>
                        <p className="text-xs text-muted-foreground">
                          Deleted by {deletion.deletedBy} on {deletion.deletionDate}
                        </p>
                        <p className="text-xs text-gray-600 mt-1">
                          Reason: {deletion.reason}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge 
                        variant={deletion.status === "Compliant" ? "default" : "secondary"}
                        className={deletion.status === "Compliant" ? "bg-green-100 text-green-700" : ""}
                      >
                        {deletion.status === "Compliant" ? (
                          <CheckCircle className="h-3 w-3 mr-1" />
                        ) : (
                          <Clock className="h-3 w-3 mr-1" />
                        )}
                        {deletion.status}
                      </Badge>
                    </div>
                  </div>
                  {index < recentDeletions.length - 1 && <Separator className="mt-4" />}
                </div>
              ))}
            </div>
            <div className="mt-4 pt-4 border-t">
              <Link href="/dashboard/auditors/deleted-items">
                <Button variant="outline" size="sm" className="w-full">
                  View All Deleted Items
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Compliance Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-green-600" />
              Government Compliance Information
            </CardTitle>
            <CardDescription>
              Teachers Council of Malawi audit compliance standards
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="font-medium text-sm mb-2">Retention Policy</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 7-year retention period (government standard)</li>
                  <li>• Secure encrypted storage</li>
                  <li>• Access limited to AUDITOR and SUPER_ADMIN roles</li>
                  <li>• Complete audit trail maintained</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-sm mb-2">Recovery Window</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 90-day recovery window for deleted items</li>
                  <li>• Automatic expiry after deadline</li>
                  <li>• Recovery requires proper authorization</li>
                  <li>• Full recovery audit trail</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}
