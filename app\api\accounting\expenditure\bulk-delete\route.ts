// app/api/accounting/expenditure/bulk-delete/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import Expenditure from '@/models/accounting/Expenditure';
import { AuditDeletionService } from '@/lib/services/audit/audit-deletion-service';

export const runtime = 'nodejs';

/**
 * POST /api/accounting/expenditure/bulk-delete
 * Bulk delete expenditure transactions
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();
    const { ids, deletionReason } = body;

    // Get client context for audit trail
    const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
    const userAgent = req.headers.get('user-agent') || 'unknown';

    // Prepare audit context
    const auditContext = {
      deletedBy: user.id,
      deletionReason,
      deletionType: 'bulk' as const,
      userInfo: {
        id: user.id,
        name: `${user.firstName} ${user.lastName}`,
        email: user.email,
        role: user.role
      },
      context: {
        ipAddress: clientIP,
        userAgent: userAgent,
        sessionId: req.headers.get('x-session-id') || undefined
      }
    };

    // Define validation rules for expenditure deletion
    const validationRules = [
      {
        field: 'status',
        disallowedValues: ['paid', 'approved'],
        errorMessage: 'Cannot delete expenditure with paid or approved status'
      }
    ];

    // Define permission check
    const permissionCheck = {
      isAdmin: hasRequiredPermissions(user, [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]),
      userId: user.id,
      checkOwnership: true,
      ownershipField: 'createdBy'
    };

    // Perform audit deletion using the service
    const result = await AuditDeletionService.performAuditDeletion(
      Expenditure,
      ids,
      auditContext,
      {
        validationRules,
        permissionCheck,
        populateFields: ['createdBy']
      }
    );

    return NextResponse.json({
      success: result.success,
      message: `Successfully moved ${result.deletedCount} expenditure transactions to audit trail`,
      deletedCount: result.deletedCount,
      auditRecordsCreated: result.auditRecordsCreated,
      requestedCount: ids.length,
      details: {
        deletedIds: result.deletedIds,
        auditRecordIds: result.auditRecordIds,
        deletionReason: result.details.deletionReason,
        auditCompliance: result.details.auditCompliance
      }
    });

  } catch (error: unknown) {
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
