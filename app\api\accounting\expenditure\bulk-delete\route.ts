// app/api/accounting/expenditure/bulk-delete/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import Expenditure from '@/models/accounting/Expenditure';
import DeletedItem from '@/models/audit/DeletedItems';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

/**
 * POST /api/accounting/expenditure/bulk-delete
 * Bulk delete expenditure transactions
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();
    const { ids, deletionReason } = body;

    // Validate deletion reason (required for audit compliance)
    if (!deletionReason || typeof deletionReason !== 'string' || deletionReason.trim().length < 10) {
      return NextResponse.json(
        { error: 'Deletion reason is required and must be at least 10 characters long for audit compliance' },
        { status: 400 }
      );
    }

    // Validate IDs
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'No expenditure IDs provided' },
        { status: 400 }
      );
    }

    // Validate that all IDs are valid MongoDB ObjectIds
    const validIds = ids.filter((id: any) => {
      return typeof id === 'string' && id.match(/^[0-9a-fA-F]{24}$/);
    });

    if (validIds.length !== ids.length) {
      return NextResponse.json(
        { error: 'Invalid expenditure IDs provided' },
        { status: 400 }
      );
    }

    logger.info('Starting bulk delete of expenditure transactions', {
      userId: user.id,
      expenditureIds: validIds,
      count: validIds.length
    });

    // Get existing expenditure transactions to check permissions and status
    const existingExpenditures = await Expenditure.find({
      _id: { $in: validIds }
    }).populate('createdBy', 'firstName lastName email');

    if (existingExpenditures.length === 0) {
      return NextResponse.json(
        { error: 'No expenditure transactions found with provided IDs' },
        { status: 404 }
      );
    }

    // Check permissions for each expenditure
    const isAdmin = hasRequiredPermissions(user, [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]);
    const unauthorizedExpenditures = existingExpenditures.filter(expenditure => {
      const createdById = (expenditure.createdBy as any)?._id?.toString() || (expenditure.createdBy as any)?.toString();
      const isCreator = createdById === user.id;
      return !isCreator && !isAdmin;
    });

    if (unauthorizedExpenditures.length > 0) {
      return NextResponse.json(
        { 
          error: `You can only delete expenditure transactions you created. ${unauthorizedExpenditures.length} transactions are not authorized for deletion.`,
          unauthorizedIds: unauthorizedExpenditures.map(e => e._id)
        },
        { status: 403 }
      );
    }

    // Check for expenditure transactions that cannot be deleted (paid/approved status)
    const nonDeletableExpenditures = existingExpenditures.filter(expenditure => 
      expenditure.status === 'paid' || expenditure.status === 'approved'
    );

    if (nonDeletableExpenditures.length > 0) {
      return NextResponse.json(
        { 
          error: `Cannot delete expenditure transactions with 'paid' or 'approved' status. ${nonDeletableExpenditures.length} transactions cannot be deleted.`,
          nonDeletableIds: nonDeletableExpenditures.map(e => e._id),
          nonDeletableReasons: nonDeletableExpenditures.map(e => ({
            id: e._id,
            expenditureNumber: e.expenditureNumber,
            status: e.status,
            reason: `Status '${e.status}' prevents deletion`
          }))
        },
        { status: 400 }
      );
    }

    // Get client IP and user agent for audit trail
    const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
    const userAgent = req.headers.get('user-agent') || 'unknown';

    // Create audit trail records for each expenditure before "deletion"
    const auditRecords = [];
    const deletedExpenditureIds = [];

    for (const expenditure of existingExpenditures) {
      try {
        // Create audit trail record
        const auditRecord = await DeletedItem.createDeletedRecord(expenditure, {
          deletedBy: user.id,
          deletionReason: deletionReason.trim(),
          deletionType: 'bulk',
          userInfo: {
            id: user.id,
            name: `${user.firstName} ${user.lastName}`,
            email: user.email,
            role: user.role
          },
          context: {
            fiscalYear: expenditure.fiscalYear,
            budgetId: expenditure.budget?.toString(),
            relatedTransactions: validIds,
            ipAddress: clientIP,
            userAgent: userAgent,
            sessionId: req.headers.get('x-session-id') || undefined
          }
        });

        auditRecords.push(auditRecord);
        deletedExpenditureIds.push(expenditure._id);

      } catch (auditError) {
        logger.error('Error creating audit record for expenditure deletion', {
          expenditureId: expenditure._id,
          error: auditError instanceof Error ? auditError.message : 'Unknown error'
        });
        // Continue with other items even if one fails
      }
    }

    // Now remove the original records from the Expenditure collection
    const deleteResult = await Expenditure.deleteMany({
      _id: { $in: deletedExpenditureIds }
    });

    logger.info('Bulk audit deletion of expenditure transactions completed', {
      userId: user.id,
      requestedCount: validIds.length,
      deletedCount: deleteResult.deletedCount,
      auditRecordsCreated: auditRecords.length,
      existingCount: existingExpenditures.length,
      deletionReason: deletionReason.trim()
    });

    return NextResponse.json({
      success: true,
      message: `Successfully moved ${deleteResult.deletedCount} expenditure transactions to audit trail`,
      deletedCount: deleteResult.deletedCount,
      auditRecordsCreated: auditRecords.length,
      requestedCount: validIds.length,
      details: {
        deletedIds: deletedExpenditureIds,
        auditRecordIds: auditRecords.map(record => record._id),
        deletedTransactions: existingExpenditures.map(expenditure => ({
          id: expenditure._id,
          expenditureNumber: expenditure.expenditureNumber,
          amount: expenditure.amount,
          title: expenditure.title,
          status: expenditure.status
        })),
        deletionReason: deletionReason.trim(),
        auditCompliance: {
          auditTrailCreated: true,
          retentionPeriod: '7 years',
          recoverable: true,
          complianceFlags: auditRecords.length > 0 ? auditRecords[0].complianceFlags : []
        }
      }
    });

  } catch (error: unknown) {
    logger.error('Error in bulk delete of expenditure transactions', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
