// app/api/accounting/expenditure/bulk-delete/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import Expenditure from '@/models/accounting/Expenditure';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

/**
 * POST /api/accounting/expenditure/bulk-delete
 * Bulk delete expenditure transactions
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();
    const { ids } = body;

    // Validate IDs
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'No expenditure IDs provided' },
        { status: 400 }
      );
    }

    // Validate that all IDs are valid MongoDB ObjectIds
    const validIds = ids.filter((id: any) => {
      return typeof id === 'string' && id.match(/^[0-9a-fA-F]{24}$/);
    });

    if (validIds.length !== ids.length) {
      return NextResponse.json(
        { error: 'Invalid expenditure IDs provided' },
        { status: 400 }
      );
    }

    logger.info('Starting bulk delete of expenditure transactions', {
      userId: user.id,
      expenditureIds: validIds,
      count: validIds.length
    });

    // Get existing expenditure transactions to check permissions and status
    const existingExpenditures = await Expenditure.find({
      _id: { $in: validIds }
    }).populate('createdBy', 'firstName lastName email');

    if (existingExpenditures.length === 0) {
      return NextResponse.json(
        { error: 'No expenditure transactions found with provided IDs' },
        { status: 404 }
      );
    }

    // Check permissions for each expenditure
    const isAdmin = hasRequiredPermissions(user, [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]);
    const unauthorizedExpenditures = existingExpenditures.filter(expenditure => {
      const isCreator = expenditure.createdBy._id.toString() === user.id;
      return !isCreator && !isAdmin;
    });

    if (unauthorizedExpenditures.length > 0) {
      return NextResponse.json(
        { 
          error: `You can only delete expenditure transactions you created. ${unauthorizedExpenditures.length} transactions are not authorized for deletion.`,
          unauthorizedIds: unauthorizedExpenditures.map(e => e._id)
        },
        { status: 403 }
      );
    }

    // Check for expenditure transactions that cannot be deleted (paid/approved status)
    const nonDeletableExpenditures = existingExpenditures.filter(expenditure => 
      expenditure.status === 'paid' || expenditure.status === 'approved'
    );

    if (nonDeletableExpenditures.length > 0) {
      return NextResponse.json(
        { 
          error: `Cannot delete expenditure transactions with 'paid' or 'approved' status. ${nonDeletableExpenditures.length} transactions cannot be deleted.`,
          nonDeletableIds: nonDeletableExpenditures.map(e => e._id),
          nonDeletableReasons: nonDeletableExpenditures.map(e => ({
            id: e._id,
            expenditureNumber: e.expenditureNumber,
            status: e.status,
            reason: `Status '${e.status}' prevents deletion`
          }))
        },
        { status: 400 }
      );
    }

    // Perform the bulk delete
    const deleteResult = await Expenditure.deleteMany({
      _id: { $in: validIds }
    });

    logger.info('Bulk delete of expenditure transactions completed', {
      userId: user.id,
      requestedCount: validIds.length,
      deletedCount: deleteResult.deletedCount,
      existingCount: existingExpenditures.length
    });

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${deleteResult.deletedCount} expenditure transactions`,
      deletedCount: deleteResult.deletedCount,
      requestedCount: validIds.length,
      details: {
        deletedIds: validIds,
        deletedTransactions: existingExpenditures.map(expenditure => ({
          id: expenditure._id,
          expenditureNumber: expenditure.expenditureNumber,
          amount: expenditure.amount,
          title: expenditure.title,
          status: expenditure.status
        }))
      }
    });

  } catch (error: unknown) {
    logger.error('Error in bulk delete of expenditure transactions', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
