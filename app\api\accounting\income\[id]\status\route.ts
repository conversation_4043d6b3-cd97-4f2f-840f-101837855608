// app/api/accounting/income/[id]/status/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';
import Income from '@/models/accounting/Income';
import { Budget, BudgetCategory, BudgetSubcategory } from '@/models/accounting/Budget';
import User from '@/models/User';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import budgetIntegrationService from '@/lib/services/accounting/budget-integration-service';
import { budgetIncomeIntegrationService } from '@/lib/services/accounting/budget-income-integration';

export const runtime = 'nodejs';

// Interface for populated income object
interface PopulatedIncome {
  _id: string;
  status: 'draft' | 'pending_approval' | 'approved' | 'rejected' | 'received' | 'cancelled';
  statusHistory?: Array<{
    status: string;
    changedBy: string;
    changedAt: Date;
    notes?: string;
    reason?: string;
  }>;
  [key: string]: unknown; // For other populated fields
}

// Status update validation schema
const statusUpdateSchema = z.object({
  status: z.enum(['draft', 'pending_approval', 'approved', 'rejected', 'received', 'cancelled']),
  notes: z.string().optional(),
  reason: z.string().optional(), // For rejections
});

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Resolve the params promise
    const { id } = await params;
    
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Ensure models are registered
    Budget;
    BudgetCategory;
    BudgetSubcategory;
    User;

    // Check permissions - only specific roles can approve income
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to update income status' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();
    logger.debug('[STATUS UPDATE] Request body received', { data });

    // Validate request body
    const validationResult = statusUpdateSchema.safeParse(data);
    if (!validationResult.success) {
      logger.error('[STATUS UPDATE] Validation failed', {
        data,
        errors: validationResult.error.errors
      });
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Find the income record
    const income = await Income.findById(id);
    if (!income) {
      return NextResponse.json(
        { error: 'Income record not found' },
        { status: 404 }
      );
    }

    // Store previous status for logging
    const previousStatus = income.status;
    const newStatus = validationResult.data.status;

    // Validate status transition
    const validTransitions: Record<string, string[]> = {
      'draft': ['pending_approval', 'approved', 'received', 'cancelled'],
      'pending_approval': ['approved', 'rejected', 'cancelled'],
      'approved': ['received', 'cancelled'],
      'rejected': ['draft', 'cancelled'],
      'received': ['cancelled'], // Can only cancel received income
      'cancelled': [] // Cannot change from cancelled
    };

    if (!validTransitions[previousStatus]?.includes(newStatus)) {
      return NextResponse.json(
        { error: `Invalid status transition from ${previousStatus} to ${newStatus}` },
        { status: 400 }
      );
    }

    // Update income status
    income.status = newStatus;
    income.updatedBy = user.id;
    income.updatedAt = new Date();

    // Add status change to history
    if (!income.statusHistory) {
      income.statusHistory = [];
    }

    income.statusHistory.push({
      status: newStatus,
      changedBy: user.id,
      changedAt: new Date(),
      notes: validationResult.data.notes || '',
      reason: validationResult.data.reason || ''
    });

    // Set specific timestamps based on status
    switch (newStatus) {
      case 'pending_approval':
        income.submittedAt = new Date();
        break;
      case 'approved':
        income.approvedAt = new Date();
        income.approvedBy = user.id;
        break;
      case 'received':
        income.receivedAt = new Date();
        income.receivedBy = user.id;
        break;
      case 'rejected':
        income.rejectedAt = new Date();
        income.rejectedBy = user.id;
        income.rejectionReason = validationResult.data.reason || '';
        break;
      case 'cancelled':
        income.cancelledAt = new Date();
        income.cancelledBy = user.id;
        break;
    }

    // Save the updated income
    await income.save();

    // Handle budget integration for approved/received income - OPTIMIZED
    if ((newStatus === 'received' || newStatus === 'approved') && income.appliedToBudget) {
      try {
        console.log('Applying income to budget after status change...');

        // Run budget integrations in parallel for better performance
        await Promise.allSettled([
          budgetIntegrationService.handleNewIncome(income),
          budgetIncomeIntegrationService.updateIncomeAsBudgetItem(income)
        ]);

        console.log('Income applied to budget successfully');
      } catch (budgetError) {
        console.warn('Budget integration failed after status change:', budgetError);
        // Don't fail the status update if budget integration fails
      }
    }

    // Remove from budget if cancelled or rejected
    if ((newStatus === 'cancelled' || newStatus === 'rejected') && income.appliedToBudget) {
      try {
        console.log('Removing income from budget after status change...');

        // ✅ NEW: Remove BudgetItem for cancelled/rejected income
        await budgetIncomeIntegrationService.removeIncomeAsBudgetItem(income);

        // You might want to implement a removeFromBudget method
        // await budgetIntegrationService.removeIncomeFromBudget(income);
        console.log('Income removed from budget successfully');
      } catch (budgetError) {
        console.warn('Budget removal failed after status change:', budgetError);
      }
    }

    // Populate the updated income for response
    const populatedIncome = await Income.findById(income._id)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .populate('approvedBy', 'firstName lastName email')
      .populate('receivedBy', 'firstName lastName email')
      .populate('budget', 'name fiscalYear')
      .populate('budgetCategory', 'name type')
      .lean();

    // Log the status change
    logger.info('Income status updated', {
      incomeId: income._id,
      previousStatus,
      newStatus,
      updatedBy: user.id,
      userEmail: user.email
    });

    return NextResponse.json({
      success: true,
      message: `Income status updated from ${previousStatus} to ${newStatus}`,
      income: populatedIncome,
      statusChange: {
        from: previousStatus,
        to: newStatus,
        updatedBy: `${user.firstName} ${user.lastName}` || user.email,
        updatedAt: new Date()
      }
    });

  } catch (error: unknown) {
    console.error('Error updating income status:', error);
    logger.error('Error updating income status', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// GET method to fetch income with status history
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Resolve the params promise
    const { id } = await params;
    
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Ensure models are registered
    Budget;
    BudgetCategory;
    BudgetSubcategory;
    User;

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Find the income record with full details
    const income = await Income.findById(id)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .populate('approvedBy', 'firstName lastName email')
      .populate('receivedBy', 'firstName lastName email')
      .populate('rejectedBy', 'firstName lastName email')
      .populate('cancelledBy', 'firstName lastName email')
      .populate('budget', 'name fiscalYear')
      .populate('budgetCategory', 'name type')
      .populate('budgetSubcategory', 'name')
      .lean() as PopulatedIncome | null;

    if (!income) {
      return NextResponse.json(
        { error: 'Income record not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      income,
      availableActions: getAvailableActions(income.status),
      statusHistory: income.statusHistory || []
    });

  } catch (error: unknown) {
    console.error('Error fetching income status details:', error);
    logger.error('Error fetching income status details', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// Helper function to get available actions based on current status
function getAvailableActions(currentStatus: string): string[] {
  const actions: Record<string, string[]> = {
    'draft': ['Submit for Approval', 'Mark as Received', 'Cancel'],
    'pending_approval': ['Approve', 'Reject', 'Cancel'],
    'approved': ['Mark as Received', 'Cancel'],
    'rejected': ['Resubmit as Draft', 'Cancel'],
    'received': ['Cancel'],
    'cancelled': []
  };

  return actions[currentStatus] || [];
}
